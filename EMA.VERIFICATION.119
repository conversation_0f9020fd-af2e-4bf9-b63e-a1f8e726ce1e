[Version: 2.3.0 
Symitar Systems, Inc. grants and the Credit Union accepts under the terms
and conditions set forth a non-exclusive and non-transferable license to
use the software programs for the internal processing requirements of the
Credit Union.  Nothing contained in this agreement shall be construed to
vest in the Credit Union any right, title, or interest in the software
programs other than as expressly granted herein and the Credit Union may
not sell, lease, sublease, assign, license, sublicense, reveal, share or
otherwise transfer such software programs.

-Abstract:
 This repgen is part of the EMA package.

-Related RepGens:
  Repgen
  EMA.INIT.119         |EMA.ACCTSHARE.119  |EMA.WORKFLOW.DEF.119     |EMA.OFAC.119 ***Modified RD file***
  EMA.NAME.119         |EMA.ADDPROD.119    |EMA.WORKFLOW.PRO.119     |
  EMA.VERIFICATION.119 |EMA.PRINTDOCS.119  |EMA.JAVASCRIPTS.PRO.119  |
  EMA.DECISION.119     |EMA.PRINTDOCS.119  |EMA.NAME.FMP.119         |

-Helpfiles used by this repgen:
  EMA.CFG.119
  EMA.CSS.119

-Tips for Running:
 Please contact your consulting representative for further details.

 Added a block to the ability to click the check qualifile box when
 currently pulling a qualifile check. This will eliminate multiple pulls.
 
 Al Zehrung 7/6/2022 - IS-1279 
 
 Updated to now check if a qualifile has been ran today and if so, it will 
 prompt the user if they wish to run another or cancel out.
 
 Al Zehrung 1/18/2024 - IS-3445
 
 Updated ID verification to ID1 only, removed ID2 and ID3
 
 Al Zehung 7/1/2024 - IS-3793]
 
SUBROUTINE DEMAND
WINDOWS

TARGET = ACCOUNT

DEFINE
  #INCLUDE "RD.WINDOWS.DEF"
  #INCLUDE "RD.GETDATA.DEF"
  #INCLUDE "RD.LOANAPP.DEF"
  #INCLUDE "RD.NAME.AND.ACCOUNT.DEF"
  #INCLUDE "EMA.WORKFLOW.DEF.119"

  FOUNDONE           = NUMBER
  ChexQualFile       = CHARACTER                    [Specfile name listed in EMA.CFG.119]
  ExperianFile       = CHARACTER                    [Specfile name listed in EMA.CFG.119]
  EquifaxFile        = CHARACTER                     [Specfile name listed in EMA.CFG.119]
  TransUnionFile     = CHARACTER                  [Specfile name listed in EMA.CFG.119]
  ViewChexQualifile  = CHARACTER
  ViewCreditReport   = CHARACTER
  InitChexSys        = NUMBER ARRAY(99)
  InitQualifile      = NUMBER ARRAY(99)
  InitPullCred       = NUMBER ARRAY(99)
  NewCredRep         = NUMBER ARRAY(99)
  CREDREPTYPE        = NUMBER
  COMPLETIONCODEDESC = CHARACTER ARRAY(10)
  USECHEXSYSTEMS     = NUMBER
  USEQUALIFILE       = NUMBER
  USEEXPERIAN        = NUMBER
  USEEQUIFAX         = NUMBER
  USETRANSUNION      = NUMBER
  PULLCREDINDEX      = NUMBER
  TEMPCHAR           = CHARACTER
  FERROR             = CHARACTER
  FNUMSDN            = NUMBER
  FNUMADD            = NUMBER
  FNUMALT            = NUMBER
  FNUMKEYED          = NUMBER

  [RTM 03/28/08 - ADDED TO POPULATE CREDIT SCORE IN APPLICATION]
  CREDREPSCORE       = NUMBER
  SPOUSECREDREPSCORE = NUMBER

  REPORTPULLED       = NUMBER
  REPORTPULLEDDISP   = CHARACTER
  EXREPTEXT          = CHARACTER
  NEWPULLFOUND       = NUMBER
  DATEHIRED          = CHARACTER
  DELETE             = NUMBER ARRAY(20)
  SIDEBARACTION      = NUMBER
  VIEWREDFLAG        = CHARACTER
  PULLQUALFLAG       = NUMBER
  QUALLOC            = NUMBER
  QUALLOCSTOUPDATE   = NUMBER ARRAY(15)
  QUALSCORE1         = NUMBER
  QUALSCORE2         = NUMBER
  QUALFOUND          = NUMBER

  MISSINGINFO=NUMBER [ADDED TO REINSTALL EMA AFTER JAVASCRIPTS.PRO GOT THIS
                      EXTRA VARIABLE; JOE K, 10/17/2019
                      (INITIALIZING TO FALSE AT BEG OF SETUP)]
                      
  PULLQUALPENDING=NUMBER
  QFDATAMATCH=NUMBER
  YESNORESPONSE=CHARACTER
  REFERRALINPUT=CHARACTER
END [DEFINE]

SETUP
  QFDATAMATCH=FALSE
  YESNORESPONSE="N"
  MISSINGINFO=FALSE
  THISSPECFILE = "VERIFICATION"
  REFERRALINPUT="E.OD.REFERRAL.INPUT"
  CALL COMMONSETUP [EMA.WORKFLOW.PRO.119]
  @ENVPARAMCHAR2 = ""
  CALL LOADVALIDNAMEINFO
  CALL LOADBUREAUINFO
  CALL LOADDESCRIPTIONS
  KEEPSCREEN = TRUE
  NINDEX = 0
  REPORTPULLED = FALSE
  REPORTPULLEDDISP = "display:none;"
  WHILE KEEPSCREEN = TRUE
  DO
    KEEPSCREEN = FALSE     
    [CUSTOMIZATION brhutch]
    [AUTOMATICALLY PULL QUALIFILE]
    IF ACCTREV = FALSE THEN
    DO
      FOR PX = 0 TO 15
      DO
        PULLQUALFLAG = 0
        PULLQUALPENDING = FALSE
        QUALLOC = 0
        [
          NUMBER1 = Name Type
          CHAR1 = TR SSN
          CODE1 = Qualifile Pulled
          CODE2 = Qualifile Score
          CODE3 = Credit Score
        ]
        FOR ACCOUNT ACCOUNT:NUMBER
        DO
          FOR EACH TRACKING WITH TRACKING:TYPE = QUALACCTTR AND (TRACKING:EXPIREDATE = '--/--/--' OR TRACKING:EXPIREDATE > SYSTEMDATE) AND
                                TRACKING:USERCHAR1 = LAPSSN(PX) AND TRACKING:USERNUMBER1 = LAPDEPENDENTAGE(PX, 9)
          DO
            PULLQUALFLAG = TRACKING:USERCODE1
            QUALLOC = TRACKING:LOCATOR
          END UNTIL QUALLOC > 0
          
          [ECU CUSTOM - CHECK FOR PENDING QUALIFILE]
          FOR EACH CREDREP 
           DO 
            IF CREDREP:SSN = LAPSSN(PX) AND (CREDREP:COMPLETIONCODE=0) THEN PULLQUALPENDING=TRUE
            CALL CHECKEXISTINGQFDATA
           END           
          [END ECU CUSTOM]
        END
        IF LAPEXIST(PX) = TRUE AND LAPLAST(PX) <> "" AND PULLQUALFLAG = 0 AND PULLQUALPENDING = FALSE AND
          (((LAACCOUNTTYPE = 0 OR LAACCOUNTTYPE = 2 OR LAACCOUNTTYPE = 3 OR LAACCOUNTTYPE = 7 OR LAACCOUNTTYPE = 13 OR
          LAACCOUNTTYPE = 14) AND
          (LAPDEPENDENTAGE(PX, 9) = 0 OR LAPDEPENDENTAGE(PX, 9) = 1 OR LAPDEPENDENTAGE(PX, 9) = 8)) OR
          (LAACCOUNTTYPE = 1 AND LAPDEPENDENTAGE(PX, 9) = 6) OR (LAACCOUNTTYPE = 6 AND LAPDEPENDENTAGE(PX, 9) = 5) OR
          (LAACCOUNTTYPE = 8 AND LAPDEPENDENTAGE(PX, 9) = 7) OR (LAACCOUNTTYPE = 9 AND LAPDEPENDENTAGE(PX, 9) = 14) OR
          (LAACCOUNTTYPE = 10 AND LAPDEPENDENTAGE(PX, 9) = 9) OR (LAACCOUNTTYPE = 15 AND LAPDEPENDENTAGE(PX, 9) = 7) OR
          (LAACCOUNTTYPE >= 30 AND LAACCOUNTTYPE <= 35 AND (LAPDEPENDENTAGE(PX, 9) = 7 OR LAPDEPENDENTAGE(PX, 9) = 9)) OR
          (LAACCOUNTTYPE = 30 AND LAPDEPENDENTAGE(PX, 9) = 0 AND LAPNAMEFORMAT(PX) = 0)) THEN
        DO
          REPORTPULLED = TRUE
          PULLCREDINDEX = 4
          FOR ACCOUNT ACCOUNT:NUMBER
          DO
            IF ACCTREV = FALSE THEN
            DO
              FOR EACH LOANAPP WITH LOANAPP:ID = LAID
              DO
                FOR EACH LOANAPP PERSON WITH (LOANAPP PERSON:LONGNAME = LAPLONGNAME(PX) AND
                                            LOANAPP PERSON:SSN = LAPSSN(PX) AND
                                            LOANAPP PERSON:DEPENDENTAGE:9 <> 2 AND LOANAPP PERSON:DEPENDENTAGE:9 = LAPDEPENDENTAGE(PX, 9))
                DO
                  INITCREDITREPORT(3)
                  IF LOANAPP PERSON:SSN="*********" OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 8 OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 9 THEN @CREDITREPORTSSN=""
                END
              END
            END
            ELSE
            DO
              FOR EACH NAME WITH (NAME:LONGNAME = LAPLONGNAME(PX) AND
                                NAME:SSN = LAPSSN(PX) AND
                                NAME:TYPE <> 2 AND NAME:TYPE = LAPDEPENDENTAGE(PX, 9))
              DO
                INITCREDITREPORT(2)
                IF NAME:SSN="*********" OR 
                   NAME:SSNTYPE = 8 OR 
                   NAME:SSNTYPE = 9 THEN @CREDITREPORTSSN=""
              END
            END
          END
          @CREDITREPORTOPTFEATURECODE = "Q"         
          CALL CREDREPRECHECK
          CALL QFPULLCHECK
          IF @CREDITREPORTERROR <> "" THEN
            POPUPMESSAGE(0, "ERROR: " + @CREDITREPORTERROR)
  
          [UPDATE THE TR TO SHOW QUAL WAS PULLED]
          ERRORTEXT = ""
          IF QUALLOC = 0 THEN
          DO
            FMPERFORM CREATE TRACKING LOC AFTERLAST (FMPPRIV, QUALACCTTR, QUALLOC, ERRORTEXT)
            DO
              SET TYPE TO QUALACCTTR
            END [FMPERFORM]
          END
          IF ERRORTEXT = "" THEN
          DO
            FMPERFORM REVISE TRACKING LOC QUALLOC (FMPPRIV, 0, ERRORTEXT)
            DO
              SET USERNUMBER1 TO LAPDEPENDENTAGE(PX, 9)
              SET USERCHAR1 TO LAPSSN(PX)
              SET USERCODE1 TO 2
            END [FMPERFORM]
            IF ERRORTEXT <> "" THEN
            DO
              POPUPMESSAGE(1, "QUAL TR REV ERR: " + ERRORTEXT)
            END
          END
          ELSE
          DO
            POPUPMESSAGE(1, "QUAL TR CR ERR: " + ERRORTEXT)
          END
        END
      END
    END
    [/CUSTOMIZATION]

    CALL VERIFICATIONSCREEN
    IF EXITAPP <> TRUE THEN
    DO
      IF ACCTREV = TRUE THEN
        CALL LOADAPPVARSTONAMES
      [*** CODE ADDED TO HANDLE SIDEBAR FUNCTIONS ***]
      FOR I = 1 TO 15
      DO
        IF DELETE(I) = TRUE THEN
        DO
          IF ACCTREV = FALSE THEN
          DO
            LAPLAST(I) = ""
            FMPERFORM DELETE LOANAPP LAID PERSON LOC LAPLOCATOR(I) (FMPPRIV, 0, FMPERROR)
            DO
            END
          END
          ELSE
          DO
            FMPERFORM REVISE NAME LOC NLOCATOR(I) (FMPPRIV, 0, FMPERROR)
            DO
              SET EXPIRATIONDATE TO SYSTEMDATE
            END
          END
          IF FMPERROR <> "" THEN
            POPUPMESSAGE(2, "Delete error=" + FMPERROR)
          ELSE
          DO
            [CALL CLEARFIELDS]
            CALL RELOADFIELDS
          END
          DELETE(I) = FALSE
        END
      END
      [*** CODE ADDED TO HANDLE SIDEBAR FUNCTIONS ***]

      IF ViewChexQualifile <> "" AND SIDEBARACTION = FALSE THEN
      DO
        IF ChexQualFile = "" THEN
          POPUPMESSAGE(0, "Chex/Qual specfile not found, please update EMA.CFG.119 helpfile.")
        ELSE
        DO
          INITSUBROUTINE(ERRORTEXT)
          EXECUTE(ChexQualFile, ERRORTEXT)
          IF ERRORTEXT <> "" THEN
            POPUPMESSAGE(0, "Chex/QualFile ERROR: " + ERRORTEXT)
        END
      END

      IF ViewCreditReport <> "" AND SIDEBARACTION = FALSE THEN
      DO
        DIALOGSTART("Select Report Type To View", 200%, 1)
        DIALOGPROMPTLISTSTART("Credit Report Bureau", 0)
        IF USEEXPERIAN = TRUE THEN
          DIALOGPROMPTLISTOPTION(1, "Experian")
        IF USEEQUIFAX = TRUE THEN
          DIALOGPROMPTLISTOPTION(2, "Equifax")
        IF USETRANSUNION = TRUE THEN
          DIALOGPROMPTLISTOPTION(3, "Trans Union")
        DIALOGPROMPTLISTEND
        DIALOGDISPLAY
        CREDREPTYPE = ENTERCODE("Credit Report Bureau", 3, 1)
        DIALOGCLOSE

        IF CREDREPTYPE = 1 THEN
        DO
          IF ExperianFile = "" THEN
            POPUPMESSAGE(0, "Cred Rep specfile not found, please update EMA.CFG.119 helpfile.")
          ELSE
          DO
            INITSUBROUTINE(ERRORTEXT)
            EXECUTE(ExperianFile, ERRORTEXT)
            IF ERRORTEXT <> "" THEN
              POPUPMESSAGE(0, "ExperianFile ERROR: " + ERRORTEXT)
          END
        END
        IF CREDREPTYPE = 2 THEN
        DO
          IF EquifaxFile = "" THEN
            POPUPMESSAGE(0, "Cred Rep specfile not found, please update EMA.CFG.119 helpfile.")
          ELSE
          DO
            INITSUBROUTINE(ERRORTEXT)
            EXECUTE(EquifaxFile, ERRORTEXT)
            IF ERRORTEXT <> "" THEN
              POPUPMESSAGE(0, "EquifaxFile ERROR: " + ERRORTEXT)
          END
        END
        IF CREDREPTYPE = 3 THEN
        DO
          IF TransUnionFile = "" THEN
            POPUPMESSAGE(0, "Cred Rep specfile not found, please update EMA.CFG.119 helpfile.")
          ELSE
          DO
            INITSUBROUTINE(ERRORTEXT)
            EXECUTE(TransUnionFile, ERRORTEXT)
            IF ERRORTEXT <> "" THEN
              POPUPMESSAGE(0, "TransUnionFile ERROR: " + ERRORTEXT)
          END
        END
      END

      [CUSTOMIZATION brhutch]
      [LAUNCH RED FLAG SPECFILE]
      IF VIEWREDFLAG <> "" AND SIDEBARACTION = FALSE THEN
      DO
        INITSUBROUTINE(ERRORTEXT)
        EXECUTE(REDFLAGSPECFILE, ERRORTEXT)
        IF ERRORTEXT <> "" THEN
          POPUPMESSAGE(0, "REDFLAG ERROR: " + ERRORTEXT)
      END
      [/CUSTOMIZATION] 

      [CUSTOMIZATION brhutch]
      [LAUNCH PREPULLED QUALIFILE SPECFILES]
      IF KEEPSCREEN = FALSE AND ACCTREV = FALSE THEN
      DO
        FOR PX = 0 TO 15
        DO
          QUALLOCSTOUPDATE(PX) = 0
        END
        FOR ACCOUNT ACCOUNT:NUMBER
        DO
          IF ChexQualFile = "" THEN
            POPUPMESSAGE(0, "Chex/Qual specfile not found, please update EMA.CFG.119 helpfile.")
          ELSE
          DO
            PX = 0
            FOR EACH TRACKING WITH TRACKING:TYPE = QUALACCTTR AND (TRACKING:EXPIREDATE = '--/--/--' OR TRACKING:EXPIREDATE > SYSTEMDATE) AND
                                   TRACKING:USERCODE1 = 2
            DO
              PX = PX + 1
            END
            FOR PY = 1 TO PX
            DO
              INITSUBROUTINE(ERRORTEXT)
              EXECUTE(ChexQualFile, ERRORTEXT)
              IF ERRORTEXT <> "" THEN
                PY = PX + 1
            END
            IF ERRORTEXT <> "" THEN
              POPUPMESSAGE(0, "Chex/QualFile ERROR: " + ERRORTEXT)
            ELSE
            DO
              FOR PX = 0 TO 15
              DO
                QUALLOCSTOUPDATE(PX) = 0
              END
              PX = 0
              FOR EACH TRACKING WITH TRACKING:TYPE = QUALACCTTR AND (TRACKING:EXPIREDATE = '--/--/--' OR TRACKING:EXPIREDATE > SYSTEMDATE) AND
                                     TRACKING:USERCODE1 = 2
              DO
                QUALLOCSTOUPDATE(PX) = TRACKING:LOCATOR
                PX = PX + 1
              END
            END
          END
        END
        PX = 0
        WHILE PX <= 15
        DO
          IF QUALLOCSTOUPDATE(PX) > 0 THEN
          DO
            FMPERFORM REVISE TRACKING LOC QUALLOCSTOUPDATE(PX) (FMPPRIV, 0, ERRORTEXT)
            DO
              SET USERCODE1 TO 1
            END [FMPERFORM]
            IF ERRORTEXT <> "" THEN
              POPUPMESSAGE(1, "QUAL TR REV(1) ERR: " + ERRORTEXT)
          END
          PX = PX + 1
        END
      END
      [/CUSTOMIZATION]

      I = 0
      WHILE LAPLAST(I) <> "" AND SIDEBARACTION = FALSE
      DO
        IF InitChexSys(I) = TRUE OR InitPullCred(I) = TRUE OR InitQualifile(I) = TRUE THEN
        DO
          [ECU CUSTOM - CHECK FOR PENDING QUALIFILE]
          FOR ACCOUNT ACCOUNT:NUMBER
          DO
            PULLQUALPENDING=FALSE
            FOR EACH CREDREP WITH CREDREP:SSN = LAPSSN(I) AND
                                   (CREDREP:COMPLETIONCODE=0)
             DO
               PULLQUALPENDING=TRUE
             END
          END
          [END ECU CUSTOM]
              
          IF InitChexSys(I) = TRUE  THEN
          DO
            PULLCREDINDEX = 4            
            FOR ACCOUNT ACCOUNT:NUMBER
            DO
              IF ACCTREV = FALSE THEN
              DO
                FOR EACH LOANAPP WITH LOANAPP:ID = LAID
                DO
                  FOR EACH LOANAPP PERSON WITH (LOANAPP PERSON:LONGNAME = LAPLONGNAME(I) AND
                                               LOANAPP PERSON:SSN = LAPSSN(I) AND
                                               LOANAPP PERSON:DEPENDENTAGE:9 <> 2)
                  DO
                    INITCREDITREPORT(3)
                    IF LOANAPP PERSON:SSN="*********" OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 8 OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 9 THEN @CREDITREPORTSSN=""
                  END
                END
              END
              ELSE
              DO
                FOR EACH NAME WITH (NAME:LONGNAME = LAPLONGNAME(I) AND
                                   NAME:SSN = LAPSSN(I) AND
                                   NAME:TYPE <> 2)
                DO
                  INITCREDITREPORT(2)
                  IF NAME:SSN="*********" OR 
                   NAME:SSNTYPE = 8 OR 
                   NAME:SSNTYPE = 9 THEN @CREDITREPORTSSN=""
                END
              END
            END
            @CREDITREPORTOPTFEATURECODE = ""           
            CALL CREDREPRECHECK
            CALL QFPULLCHECK
            IF @CREDITREPORTERROR <> "" THEN
              POPUPMESSAGE(0, "ERROR: " + @CREDITREPORTERROR)
          END

          IF InitQualifile(I) = TRUE AND PULLQUALPENDING = FALSE THEN
          DO 
            PULLCREDINDEX = 4       
            FOR ACCOUNT ACCOUNT:NUMBER
            DO               
              IF ACCTREV = FALSE THEN
              DO
                FOR EACH LOANAPP WITH LOANAPP:ID = LAID
                DO
                  FOR EACH LOANAPP PERSON WITH (LOANAPP PERSON:LONGNAME = LAPLONGNAME(I) AND
                                               LOANAPP PERSON:SSN = LAPSSN(I) AND
                                               LOANAPP PERSON:DEPENDENTAGE:9 <> 2)
                  DO
                    INITCREDITREPORT(3)
                    IF LOANAPP PERSON:SSN="*********" OR 
                       LOANAPP PERSON:DEPENDENTAGE:10 = 8 OR 
                       LOANAPP PERSON:DEPENDENTAGE:10 = 9 THEN @CREDITREPORTSSN=""                    
                  END
                END
              END
              ELSE
              DO
                FOR EACH NAME WITH (NAME:LONGNAME = LAPLONGNAME(I) AND
                                   NAME:SSN = LAPSSN(I) AND
                                   NAME:TYPE <> 2)
                DO
                  INITCREDITREPORT(2)
                  IF NAME:SSN="*********" OR 
                   NAME:SSNTYPE = 8 OR 
                   NAME:SSNTYPE = 9 THEN @CREDITREPORTSSN=""                              
                END
              END
            END
            @CREDITREPORTOPTFEATURECODE = "Q"
            CALL CREDREPRECHECK
            CALL QFPULLCHECK
            IF @CREDITREPORTERROR <> "" THEN
              POPUPMESSAGE(0, "ERROR: " + @CREDITREPORTERROR)
          END

          IF InitPullCred(I) = TRUE THEN
          DO
            PULLCREDINDEX = NewCredRep(I)
            FOR ACCOUNT ACCOUNT:NUMBER
            DO
              IF ACCTREV = FALSE THEN
              DO
                FOR EACH LOANAPP WITH LOANAPP:ID = LAID
                DO
                  FOR EACH LOANAPP PERSON WITH (LOANAPP PERSON:LONGNAME = LAPLONGNAME(I) AND
                                               LOANAPP PERSON:SSN = LAPSSN(I) AND
                                               LOANAPP PERSON:DEPENDENTAGE:9 <> 2)
                  DO
                    INITCREDITREPORT(3)
                    IF LOANAPP PERSON:SSN="*********" OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 8 OR 
                     LOANAPP PERSON:DEPENDENTAGE:10 = 9 THEN @CREDITREPORTSSN=""
                  END
                END
              END
              ELSE
              DO
                FOR EACH NAME WITH (NAME:LONGNAME = LAPLONGNAME(I) AND
                                   NAME:SSN = LAPSSN(I) AND
                                   NAME:TYPE <> 2)
                DO
                  INITCREDITREPORT(2)
                  IF NAME:SSN="*********" OR 
                   NAME:SSNTYPE = 8 OR 
                   NAME:SSNTYPE = 9 THEN @CREDITREPORTSSN=""
                END
              END
            END           
            CALL CREDREPRECHECK
            CALL QFPULLCHECK
            IF @CREDITREPORTERROR <> "" THEN
              POPUPMESSAGE(0, "ERROR: " + @CREDITREPORTERROR)
          END
        END
        I = I + 1
      END

      [CUSTOMIZATION brhutch]
      [STORE QUALIFILE SCORES]
      IF ACCTREV = FALSE THEN
      DO
        QUALSCORE1 = 0
        QUALSCORE2 = 0
        FOR PX = 0 TO 15
        DO
          IF LAPEXIST(PX) = TRUE AND LAPLAST(PX) <> "" AND LAPSSN(PX) <> "" THEN
          DO
            QUALFOUND = FALSE
            FOR ACCOUNT ACCOUNT:NUMBER
            DO
              FOR EACH CREDREP WITH CREDREP:BUREAU = 4 AND CREDREP:INCLUDEQUALIFILE AND CREDREP:SSN = LAPSSN(PX) AND CREDREP:COMPLETIONCODE = 1
              DO
                FOR EACH CREDREP ITEM WITH CREDREP ITEM:SEGMENTTYPE = "0129"
                DO
                  QUALSCORE1 = VALUE(CREDREP ITEM:CH4:1)
                END
                FOR EACH CREDREP ITEM WITH CREDREP ITEM:SEGMENTTYPE = "0127"
                DO
                  QUALSCORE2 = VALUE(CREDREP ITEM:CH4:3)
                END
                QUALFOUND = TRUE
              END
  
              QUALLOC = 0
              FOR EACH TRACKING WITH TRACKING:TYPE = QUALACCTTR AND (TRACKING:EXPIREDATE = '--/--/--' OR TRACKING:EXPIREDATE > SYSTEMDATE) AND
                                    TRACKING:USERCHAR1 = LAPSSN(PX) AND TRACKING:USERNUMBER1 = LAPDEPENDENTAGE(PX, 9)
              DO
                QUALLOC = TRACKING:LOCATOR
              END UNTIL QUALLOC > 0
            END
            IF QUALFOUND = TRUE THEN
            DO
              ERRORTEXT = ""
              IF QUALLOC = 0 THEN
              DO
                FMPERFORM CREATE TRACKING LOC AFTERLAST (FMPPRIV, QUALACCTTR, QUALLOC, ERRORTEXT)
                DO
                  SET TYPE TO QUALACCTTR
                END [FMPERFORM]
              END
              IF ERRORTEXT = "" THEN
              DO
                FMPERFORM REVISE TRACKING LOC QUALLOC (FMPPRIV, 0, ERRORTEXT)
                DO
                  SET USERCODE2 TO QUALSCORE1
                  SET USERCODE3 TO QUALSCORE2
                END [FMPERFORM]
                IF ERRORTEXT <> "" THEN
                DO
                  POPUPMESSAGE(1, "QUAL TR REV(2) ERR: " + ERRORTEXT)
                END
              END
              ELSE
              DO
                POPUPMESSAGE(1, "QUAL TR CR ERR: " + ERRORTEXT)
              END
            END
          END
        END
      END
      [/CUSTOMIZATION]
    END
  END
  IF ACCTREV = FALSE AND EXITAPP <> TRUE THEN
    CALL GETCREDSCORE
END [SETUP]

PRINT TITLE = "VERIFICATION SCREEN"
  SUPPRESSNEWLINE
END [PRINT]

PROCEDURE VERIFICATIONSCREEN
  [***START REFERRAL POPUP PROCESS]
  INITSUBROUTINE(ERRORTEXT)
  EXECUTE(REFERRALINPUT,1,ERRORTEXT)
  IF ERRORTEXT<>"" THEN
  POPUPMESSAGE(0, "Referral popup ERROR: " + ERRORTEXT)
  [***END REFERRAL POPUP]
  HTMLVIEWOPEN
  HTMLVIEWLINE("<!DOCTYPE html PUBLIC " + Q + "-//W3C//DTD HTML 4.01 Transitional//EN" + Q)
  HTMLVIEWLINE(Q + "http://www.w3.org/TR/html4/loose.dtd" + Q + ">")
  HTMLVIEWLINE("<html>")
  HTMLVIEWLINE("<head>")
  HTMLVIEWLINE("<title>Verification</title>")
  CALLINGSCREEN = "VER"
  CALL CHECKHTMLFILE
  CALL SHOWHIDESCRIPTS
  CALL DISABLEENTER
  CALL ENABLEREFESHBUTTON
  CALL EXITYESNO
  CALL DELETEYESNO
  [** This calls the procedure to load the CSS code for the]
  [   screens appearance **]
  CALL LOADCSS
  HTMLVIEWLINE("</head>")
  HTMLVIEWLINE("")
  HTMLVIEWLINE("")
  HTMLVIEWLINE("<body onLoad='checkFILES()'>")
  HTMLVIEWLINE("")
  HTMLVIEWLINE("<form name='EMAForm' METHOD='POST' ACTION='symitar://HTMLView~Action=Post" + NOCLOSE + "'>")

  IF ACCTREV = FALSE THEN
  DO
    HEADERDESC = "Applicant Verification"
    SHOWWORKFLOW = TRUE
  END
  ELSE
  DO
    HEADERDESC = "Account Revision - Verification"
    SHOWWORKFLOW = FALSE
  END

  CALL PAGEHEADER

  HTMLVIEWLINE("<div id='left'>")
  CALL NAMESIDEBAR
  HTMLVIEWLINE("</div>") [left]

  HTMLVIEWLINE("<div id='content'>")

  IF USEEXPERIAN = TRUE OR USEEQUIFAX = TRUE OR USETRANSUNION = TRUE OR
     USECHEXSYSTEMS = TRUE OR USEQUALIFILE = TRUE THEN
  DO
    HTMLVIEWLINE("<table align='center' border='0' width='90%'><tr>")
    IF USECHEXSYSTEMS = TRUE OR USEQUALIFILE = TRUE THEN
    DO
      HTMLVIEWLINE("<td align='center'>")
      HTMLVIEWLINE(" <input type='hidden' name='ViewChexQualifile' value=''>")
      HTMLVIEWLINE("<button type=submit class='runspec'")
      HTMLVIEWLINE("  onClick='document.EMAForm.ViewChexQualifile.value=" + Q + "OK" + Q + ";'")
      HTMLVIEWLINE("  value='ViewChexQualifile'><strong>View Chex/Qualifile...</strong></button>")
      HTMLVIEWLINE("</td>")
    END
    IF USEEXPERIAN = TRUE OR USEEQUIFAX = TRUE OR USETRANSUNION = TRUE THEN
    DO
      HTMLVIEWLINE("<td align='center'>")
      HTMLVIEWLINE(" <input type='hidden' name='ViewCreditReport' value=''>")
      HTMLVIEWLINE("<button type=submit class='runspec'")
      HTMLVIEWLINE("  onClick='document.EMAForm.ViewCreditReport.value=" + Q + "YES" + Q + ";'")
      HTMLVIEWLINE("  value='ViewCreditReport'><strong>View Credit Report...</strong></button>")
      HTMLVIEWLINE("</td>")
    END
    [CUSTOMIZATION brhutch]
    [RED FLAG BUTTON]
    IF REDFLAGSPECFILE <> "" THEN
    DO
      HTMLVIEWLINE("<td align='center'>")
      HTMLVIEWLINE(" <input type='hidden' name='ViewRedFlag' value=''>")
      HTMLVIEWLINE("<button type=submit class='runspec'")
      HTMLVIEWLINE("  onClick='document.EMAForm.ViewRedFlag.value=" + Q + "OK" + Q + ";'")
      HTMLVIEWLINE("  value='ViewRedFlag'><strong>Red Flag...</strong></button>")
      HTMLVIEWLINE("</td>")
    END
    [/CUSTOMIZATION]
    HTMLVIEWLINE("</tr>")
    HTMLVIEWLINE("</table>")
  END

  HTMLVIEWLINE("<table align='center' border='0' width='90%'><tr><td>")
  NINDEX = 0
  WHILE LAPLAST(NINDEX) <> ""
  DO   
    IF LAPDEPENDENTAGE(NINDEX, 9) <> 2 THEN
    DO
      HTMLVIEWLINE("<hr width='100%'>")
      HTMLVIEWLINE("<table align='center' border='0' width='100%' class='drkblue'>")
      HTMLVIEWLINE(" <tr><td colspan='3'><span class='white'>")
      HTMLVIEWLINE("   <strong>" + NAMETYPEDESC(0, LAPDEPENDENTAGE(NINDEX, 9)) + "</strong></span>")
      HTMLVIEWLINE(" </td></tr>")

      HTMLVIEWLINE(" <tr><td width='30%' valign='top' nowrap class='main'>")
      IF LAPEXTENDEDNAME(NINDEX) = "" THEN
        HTMLVIEWLINE("  " + LAPLONGNAME(NINDEX) + "<br>")
      ELSE
        HTMLVIEWLINE("  " + LAPLONGNAME(NINDEX) + "...<br>")
      HTMLVIEWLINE("  " + LAPSTREET(NINDEX) + "<br>")
      IF LAPSTATE(NINDEX) <> "" THEN
        HTMLVIEWLINE("  " + LAPCITY(NINDEX) + ", " + LAPSTATE(NINDEX) + " " + LAPZIPCODE(NINDEX))
      ELSE
        HTMLVIEWLINE("  " + LAPCITY(NINDEX) + ", " + LAPCOUNTRY(NINDEX) + " " + LAPZIPCODE(NINDEX))
      HTMLVIEWLINE(" </td>")

      HTMLVIEWLINE(" <td width='30%' valign='top' nowrap class='main'>")
      HTMLVIEWLINE("  <table align='left' border='0'>")      

      [***Pull ChexSystems Section***]
      IF USECHEXSYSTEMS = TRUE THEN
      DO
        HTMLVIEWLINE(" <tr><td colspan='2' nowrap>")
        HTMLVIEWLINE("   <input type='checkbox' name='InitChexSys(" + FORMAT("99", NINDEX) + ")'")
        HTMLVIEWLINE("          id='InitChexSys(" + FORMAT("99", NINDEX) + ")'")
        IF LAPSTATE(NINDEX) = "" THEN
          HTMLVIEWLINE("         disabled")
        HTMLVIEWLINE("          onclick='javascript:enableRefresh(this);' value='1'>")
        HTMLVIEWLINE("   <label for='InitChexSys(" + FORMAT("99", NINDEX) + ")'>Pull ChexSystems</label>")
        HTMLVIEWLINE(" </td></tr>")
      END
      [***Pull Qualifle Section***]
      IF USEQUALIFILE = TRUE THEN
      DO
       [ECU CUSTOM - CHECK FOR PENDING QUALIFILE]
        FOR ACCOUNT ACCOUNT:NUMBER
         DO
          FOR EACH CREDREP WITH CREDREP:SSN = LAPSSN(NINDEX) AND
                                CREDREP:COMPLETIONCODE=0
          DO      
           PULLQUALPENDING=TRUE
          END
         END
        [END ECU CUSTOM]
        [CUSTOMIZATION brhutch]
        [DO NO DISPLAY PULL QUALIFILE CHECKBOX IF CERTAIN CONDITIONS ARE MET]
        OLDI = I
        OLDJ = J
        LISTINPUT = NOQUALIFILEACCTTYPES
        IF CHARACTERSEARCH(LISTINPUT, "ALL") > 0 THEN
          LISTINPUT = "0001-9999"
        CALL BIGLISTEXPAND
        I = OLDI
        J = OLDJ
        WHILELIMIT = 100000
        IF (SEGMENT(LIST(NUMBER(LAACCOUNTTYPE / 100)), NUMBER(MOD(LAACCOUNTTYPE, 100)) + 1, NUMBER(MOD(LAACCOUNTTYPE, 100)) + 1) = "1" AND
           LAPDEPENDENTAGE(NINDEX, 9) = 0) OR
           (LAACCOUNTTYPE = 30 AND LAPNAMEFORMAT(NINDEX) = 1 AND LAPDEPENDENTAGE(NINDEX, 9) = 0) OR LAPNAMEFORMAT(NINDEX) = 4 OR
           PULLQUALPENDING=TRUE THEN
        DO
        END
        ELSE
        DO
          [/CUSTOMIZATION]          
          HTMLVIEWLINE("  <tr><td colspan='2' nowrap>")          
          HTMLVIEWLINE("      <input type='checkbox' name='InitQualifile(" + FORMAT("99", NINDEX) + ")'")
          HTMLVIEWLINE("          id='InitQualifile(" + FORMAT("99", NINDEX) + ")'")
          IF LAPSTATE(NINDEX) = "" THEN
           HTMLVIEWLINE("            disabled")
          HTMLVIEWLINE("             onclick='javascript:enableRefresh(this);' value='1'>")
          HTMLVIEWLINE("   <label for='InitQualifile(" + FORMAT("99", NINDEX) + ")'>Pull Qualifile</label>")
          HTMLVIEWLINE(" </td></tr>")          
          [CUSTOMIZATION brhutch]
        END
        [/CUSTOMIZATION]

      END
      PULLQUALPENDING =FALSE
      [***Pull Credit Report Section***]
      IF USEEXPERIAN = TRUE OR USEEQUIFAX = TRUE OR USETRANSUNION = TRUE THEN
      DO
        HTMLVIEWLINE("  <tr><td width='10%' nowrap>")
        HTMLVIEWLINE("      <input type='checkbox' name='InitPullCred(" + FORMAT("99", NINDEX) + ")' value='1' ")
        HTMLVIEWLINE("        id='InitPullCred(" + FORMAT("99", NINDEX) + ")'")
        HTMLVIEWLINE("        onClick=" + Q + "javascript:showhide('CredRepOptions" + FORMAT("99", NINDEX) + "');enableRefresh(this);" + Q + ">")
        HTMLVIEWLINE("       <label for='InitPullCred(" + FORMAT("99", NINDEX) + ")'>Pull Credit</label>&nbsp;")
        HTMLVIEWLINE("      </td>")
        HTMLVIEWLINE("     <td width='90%' nowrap>")
        HTMLVIEWLINE("       <select id='CredRepOptions" + FORMAT("99", NINDEX) + "' style='display:none;' name='NewCredRep(" + FORMAT("99", NINDEX) + ")'>")
        IF USEEXPERIAN = TRUE THEN
          HTMLVIEWLINE("         <option value='1'>Experian")
        IF USEEQUIFAX = TRUE AND LAPSTATE(NINDEX) <> "" THEN
          HTMLVIEWLINE("         <option value='2'>Equifax")
        IF USETRANSUNION = TRUE THEN
          HTMLVIEWLINE("         <option value='3'>Trans Union")
        HTMLVIEWLINE("        </select>")
        HTMLVIEWLINE("      </td>")
      END
      HTMLVIEWLINE(" </table>")
      HTMLVIEWLINE("</td>")
      HTMLVIEWLINE("<td width='40%' valign=top  nowrap class='main'>")
      HTMLVIEWLINE(" <table align='left' border='0'>")
      HTMLVIEWLINE("  <tr><td nowrap><b><u>Existing Reports:</u></b>")
      FOR ACCOUNT ACCOUNT:NUMBER
      DO
        FOR EACH CREDREP WITH (CREDREP:SSN = LAPSSN(NINDEX) OR
                              (CREDREP:SSN = "" AND 
                               CREDREP:FIRSTNAME=UPPERCASE(LAPFIRST(NINDEX)) AND 
                               CREDREP:LASTNAME=UPPERCASE(LAPLAST(NINDEX)))) AND
                               CREDREP:BUREAU = 4 AND
                               CREDREP:OPTFEATURECODE <> "Q"
        DO
          EXREPTEXT = "ChexSystems - "
          CALL CREATEREPTEXT
        END
        FOR EACH CREDREP WITH (CREDREP:SSN = LAPSSN(NINDEX) OR
                              (CREDREP:SSN = "" AND 
                               CREDREP:FIRSTNAME=UPPERCASE(LAPFIRST(NINDEX)) AND 
                               CREDREP:LASTNAME=UPPERCASE(LAPLAST(NINDEX)))) AND
                               CREDREP:BUREAU = 4 AND
                               CREDREP:OPTFEATURECODE = "Q"
        DO
          EXREPTEXT = "Qualifile - "
          CALL CREATEREPTEXT
        END
        FOR EACH CREDREP WITH (CREDREP:SSN = LAPSSN(NINDEX) OR
                              (CREDREP:SSN = "" AND 
                               CREDREP:FIRSTNAME=UPPERCASE(LAPFIRST(NINDEX)) AND 
                               CREDREP:LASTNAME=UPPERCASE(LAPLAST(NINDEX)))) AND
                               CREDREP:BUREAU = 1
        DO
          EXREPTEXT = "Experian - "
          CALL CREATEREPTEXT
        END
        FOR EACH CREDREP WITH (CREDREP:SSN = LAPSSN(NINDEX) OR
                              (CREDREP:SSN = "" AND 
                               CREDREP:FIRSTNAME=UPPERCASE(LAPFIRST(NINDEX)) AND 
                               CREDREP:LASTNAME=UPPERCASE(LAPLAST(NINDEX)))) AND
                               CREDREP:BUREAU = 2
        DO
          EXREPTEXT = "Equifax - "
          CALL CREATEREPTEXT
        END
        FOR EACH CREDREP WITH (CREDREP:SSN = LAPSSN(NINDEX) OR
                              (CREDREP:SSN = "" AND 
                               CREDREP:FIRSTNAME=UPPERCASE(LAPFIRST(NINDEX)) AND 
                               CREDREP:LASTNAME=UPPERCASE(LAPLAST(NINDEX)))) AND
                               CREDREP:BUREAU = 3
        DO
          EXREPTEXT = "Trans Union - "
          CALL CREATEREPTEXT
        END
      END

      IF NINDEX = 0 AND DEMOSYSTEM = TRUE THEN
      DO
        HTMLVIEWLINE("  <br><span class='valid'>Chexsystems - Completed 01/01/01 99:99</span>")
        HTMLVIEWLINE("  <br><span class='invalid'>Chexsystems - Pending 01/01/01 99:99</span>")
        HTMLVIEWLINE("  <br><span class='valid'>Qualifile - Completed 01/01/01 99:99</span>")
        HTMLVIEWLINE("  <br><span class='valid'>Experian - Completed 01/01/01 99:99</span>")
        HTMLVIEWLINE("  <br><span class='invalid'>Experian - Incomplete 01/01/01 99:99</span>")
      END

      HTMLVIEWLINE("</td></tr>")
      HTMLVIEWLINE("</table></table><br>")
    END
    NINDEX = NINDEX + 1
  END
  HTMLVIEWLINE("</table>")
  HTMLVIEWLINE("<table align='center' border='0'>")
  HTMLVIEWLINE(" <tr align=center>")
  HTMLVIEWLINE("  <td id='continue'>")
  HTMLVIEWLINE("   <button type=submit class='continue'")
  HTMLVIEWLINE("           value='continue'><strong>Continue</strong></button>")
  HTMLVIEWLINE("  </td>")
  HTMLVIEWLINE("  <td id='pullreport' style='display:none'>")
  HTMLVIEWLINE("  <input type='hidden' name='PullReport' value=''>")
  HTMLVIEWLINE("  <button type=submit")
  HTMLVIEWLINE("   onClick='document.EMAForm.PullReport.value=" + Q + "OK" + Q + ";'")
  HTMLVIEWLINE("   value='PullReport'><strong>Pull Report</strong></button>")
  HTMLVIEWLINE("  </td>")
  IF REPORTPULLED = TRUE THEN
    REPORTPULLEDDISP = "display:block;"
  ELSE
    REPORTPULLEDDISP = "display:none;"
  HTMLVIEWLINE("  <td id='refresh' style='" + REPORTPULLEDDISP + "'>")
  HTMLVIEWLINE("  <input type='hidden' name='Refresh' value=''>")
  HTMLVIEWLINE("  <button type=submit")
  HTMLVIEWLINE("   onClick='document.EMAForm.Refresh.value=" + Q + "OK" + Q + ";'")
  HTMLVIEWLINE("   value='Refresh'><strong>Refresh</strong></button>")
  HTMLVIEWLINE("  </td>")

  HTMLVIEWLINE("  <td>")
  HTMLVIEWLINE("   <input type='hidden' name='ExitApp' value=''>")
  HTMLVIEWLINE("     <button type=submit class='exit' onClick='return exitYesNo();'")
  HTMLVIEWLINE("             value='continue'><strong>Exit</strong></button>")
  HTMLVIEWLINE("  </td>")

  HTMLVIEWLINE("</table>")
  HTMLVIEWLINE("</div>") [content]

  HTMLVIEWLINE("</form>")
  HTMLVIEWLINE("</body>")
  HTMLVIEWLINE("</html>")
  HTMLVIEWDISPLAY

  [** THE FOLLOWING CODE WILL READ THE INFORMATION FROM **]
  [** FROM THE SCREEN **]
  INPUTCOUNT = 0
  WHILELIMIT = 100000
  ViewChexQualifile = ""
  ViewCreditReport = ""
  [CUSTOMIZATION brhutch]
  VIEWREDFLAG = ""
  [/CUSTOMIZATION]
[  InitOFAC = -1 - remove ofac joe k 12/17/18]
  KEEPSCREEN = FALSE
  NEWPULLFOUND = FALSE
  SIDEBARACTION = FALSE
  FOR I = 0 TO 99
  DO
    InitChexSys(I) = 0
    InitQualifile(I) = 0
    InitPullCred(I) = 0
    NewCredRep(I) = 0
  END
  INPUT = ""
  WHILE INPUT <> "EOD"
  DO
    INPUT = ENTERLINE(0)
    INPUTCOUNT = INPUTCOUNT + 1
    INPUTFORM(INPUTCOUNT) = INPUT
    THEFIELD = SEGMENT(INPUT, 1, CHARACTERSEARCH(INPUT, "=") - 1)
    THEVALUE = SEGMENT(INPUT, CHARACTERSEARCH(INPUT, "=") + 1, LENGTH(INPUT))
    CALL READSCREEN
  END

  IF NEWPULLFOUND = TRUE THEN
    REPORTPULLED = TRUE
  CALL DEBUGSCREEN
END [PROCEDURE]

PROCEDURE READSCREEN
  IF THEFIELD = "workflowstep" THEN
    @ENVPARAMCODE1 = VALUE(THEVALUE)
  ELSE IF CHARACTERSEARCH(THEFIELD, "Delete") <> FALSE AND
          VALUE(THEVALUE) <> 99 THEN
  DO
    LAPLAST(VALUE(THEVALUE)) = ""
    DELETE(VALUE(THEVALUE)) = TRUE
    SIDEBARACTION = TRUE
    KEEPSCREEN = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "Modify") <> FALSE AND
          VALUE(THEVALUE) <> 99 THEN
  DO
    IF ACCTREV = TRUE THEN
    DO
      @ENVPARAMCODE1 = 1
    END
    ELSE
    DO
      @ENVPARAMCODE1 = 2
    END
    @ENVPARAMCHAR2 = "MOD"
    @ENVPARAMCODE3 = VALUE(THEVALUE)
    SIDEBARACTION = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "CreateAnotherName") <> FALSE AND
          VALUE(THEVALUE) <> 0 THEN
  DO
    IF ACCTREV = TRUE THEN
    DO
      @ENVPARAMCODE1 = 1
    END
    ELSE
    DO
      @ENVPARAMCODE1 = 2
    END
    @ENVPARAMCHAR2 = "ADD"
    @ENVPARAMCODE4 = VALUE(THEVALUE)
    SIDEBARACTION = TRUE
  END
  ELSE IF THEFIELD = "ViewChexQualifile" AND THEVALUE <> "" THEN
  DO
    ViewChexQualifile = THEVALUE
    KEEPSCREEN = TRUE
  END
  ELSE IF THEFIELD = "ViewCreditReport" AND THEVALUE <> "" THEN
  DO
    ViewCreditReport = THEVALUE
    KEEPSCREEN = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "InitChexSys") > 0 THEN
  DO
    InitChexSys(VALUE(THEFIELD)) = VALUE(THEVALUE)
    NEWPULLFOUND = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "InitQualifile") > 0 THEN
  DO
    InitQualifile(VALUE(THEFIELD)) = VALUE(THEVALUE)
    NEWPULLFOUND = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "InitPullCred") > 0 THEN
  DO
    InitPullCred(VALUE(THEFIELD)) = VALUE(THEVALUE)
    NEWPULLFOUND = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "NewCredRep") > 0 THEN
    NewCredRep(VALUE(THEFIELD)) = VALUE(THEVALUE)
  ELSE IF THEFIELD = "Refresh" AND THEVALUE <> "" THEN
    KEEPSCREEN = TRUE
  ELSE IF THEFIELD = "PullReport" AND THEVALUE <> "" THEN
  DO
    KEEPSCREEN = TRUE
    REPORTPULLED = TRUE
  END
  ELSE IF CHARACTERSEARCH(THEFIELD, "ExitApp") <> FALSE AND THEVALUE = "Exit" THEN
  DO
    EXITAPP = TRUE
    KEEPSCREEN = FALSE
    @ENVPARAMCODE1 = 999
  END
  [CUSTOMIZATION brhutch]
  ELSE IF THEFIELD = "ViewRedFlag" AND THEVALUE <> "" THEN
  DO
    VIEWREDFLAG = THEVALUE
    KEEPSCREEN = TRUE
  END
  [/CUSTOMIZATION]
END [PROCEDURE]


PROCEDURE LOADBUREAUINFO
[  USEOFAC = FALSE - remove ofac joe k 12/17/18]
  USECHEXSYSTEMS = FALSE
  USEQUALIFILE = FALSE
  USEEXPERIAN = FALSE
  USEEQUIFAX = FALSE
  USETRANSUNION = FALSE
  FILEOPEN("HELP", "EMA.CFG.119", "READ", FNUM, ERRORTEXT)
  FLINE = ""
  WHILE CHARACTERSEARCH(FLINE, "**BEGIN VALID VERIFICATION METHODS**") = 0
  DO
    FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  END
  FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  WHILE CHARACTERSEARCH(FLINE, "**END VALID VERIFICATION METHODS**") = 0
  DO
    IF CHARACTERSEARCH(UPPERCASE(FLINE), "CHEXSYSTEMS") > 0 AND
            CHARACTERSEARCH(SEGMENT(UPPERCASE(FLINE), 1, 6), "ON") > 0 THEN
      USECHEXSYSTEMS = TRUE
    ELSE IF CHARACTERSEARCH(UPPERCASE(FLINE), "QUALIFILE") > 0 AND
            CHARACTERSEARCH(SEGMENT(UPPERCASE(FLINE), 1, 6), "ON") > 0 THEN
      USEQUALIFILE = TRUE
    ELSE IF CHARACTERSEARCH(UPPERCASE(FLINE), "EXPERIAN") > 0 AND
            CHARACTERSEARCH(SEGMENT(UPPERCASE(FLINE), 1, 6), "ON") > 0 THEN
      USEEXPERIAN = TRUE
    ELSE IF CHARACTERSEARCH(UPPERCASE(FLINE), "EQUIFAX") > 0 AND
            CHARACTERSEARCH(SEGMENT(UPPERCASE(FLINE), 1, 6), "ON") > 0 THEN
      USEEQUIFAX = TRUE
    ELSE IF CHARACTERSEARCH(UPPERCASE(FLINE), "TRANSUNION") > 0 AND
            CHARACTERSEARCH(SEGMENT(UPPERCASE(FLINE), 1, 6), "ON") > 0 THEN
      USETRANSUNION = TRUE
    FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  END

  FLINE = ""
  WHILE CHARACTERSEARCH(FLINE, "**BEGIN VERIFICATION SPECFILES**") = 0
  DO
    FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  END
  FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  WHILE CHARACTERSEARCH(FLINE, "**END VERIFICATION SPECFILES**") = 0
  DO
    IF CHARACTERSEARCH(FLINE, "Chex/Qualifile") > 0 THEN
      ChexQualFile = SEGMENT(FLINE, 30, LENGTH(FLINE))
    ELSE IF CHARACTERSEARCH(FLINE, "Experian") > 0 THEN
      ExperianFile = SEGMENT(FLINE, 30, LENGTH(FLINE))
    ELSE IF CHARACTERSEARCH(FLINE, "Equifax") > 0 THEN
      EquifaxFile = SEGMENT(FLINE, 30, LENGTH(FLINE))
    ELSE IF CHARACTERSEARCH(FLINE, "TransUnion") > 0 THEN
      TransUnionFile = SEGMENT(FLINE, 30, LENGTH(FLINE))
    FILEREADLINE(FNUM, FLINE, ERRORTEXT)
  END
  FILECLOSE(FNUM, ERRORTEXT)
  IF ERRORTEXT <> "" THEN
    POPUPMESSAGE(0, "ERR EMA.CFG.119 - " + ERRORTEXT)
END [PROCEDURE]

PROCEDURE LOADDESCRIPTIONS
  COMPLETIONCODEDESC(0) = "Pending"
  COMPLETIONCODEDESC(1) = "Completed"
  COMPLETIONCODEDESC(2) = "Too Large"
  COMPLETIONCODEDESC(3) = "Incomplete"
  COMPLETIONCODEDESC(4) = "Canceled"
  COMPLETIONCODEDESC(5) = "Modem Problem"
  COMPLETIONCODEDESC(6) = "Protocol Problem"
END [PROCEDURE]

PROCEDURE CREATEREPTEXT
  EXREPTEXT = EXREPTEXT + COMPLETIONCODEDESC(CREDREP:COMPLETIONCODE) +
  " " + FORMAT("99/99/99", CREDREP:COMPLETIONDATE) +
  " " + FORMAT("99:99", CREDREP:COMPLETIONTIME)
  IF CREDREP:COMPLETIONCODE = 1 THEN
    EXREPTEXT = "<span class='valid'>" + EXREPTEXT + "</span>"
  ELSE
    EXREPTEXT = "<span class='invalid'>" + EXREPTEXT + "</span>"
  HTMLVIEWLINE("  <br>" + EXREPTEXT)
END [PROCEDURE]

PROCEDURE INITCHEXORCREDITPULL
  @CREDITREPORTBUREAU = PULLCREDINDEX
  IF PULLCREDINDEX = 1 THEN
    [EXPERIAN]
  DO
    @CREDITREPORTRISKMODEL1 = "AA"
    @CREDITREPORTOPERATORCODE = SEGMENT(SYSUSERNAME(SYSUSERNUMBER), 1, 2)
  END
  IF PULLCREDINDEX = 3 THEN
    [TRANSUNION]
  DO
    IF LAPCURREMPSTARTDATE(I) = '--/--/----' THEN
      DATEHIRED = FORMAT("********", SYSTEMDATE)
    ELSE
      DATEHIRED = FORMAT("********", LAPCURREMPSTARTDATE(I))
    @CREDITREPORTDATEHIRED = SEGMENT(DATEHIRED, 7, 8) + SEGMENT(DATEHIRED, 1, 2)
  END
  IF PULLCREDINDEX = 4 THEN
    [CHEXSYSTEMS]
  DO
    IF CHARACTERSEARCH(BUSINESSACCTTYPES, FORMAT("9999", LAACCOUNTTYPE)) > 0 AND
       LAPNAMEFORMAT(I) = 1 THEN
      @CREDITREPORTCOMMERCIALCODE = 1 [Allow pull for truncated business names]
    IF @CREDITREPORTOPTFEATURECODE = "Q" THEN
      [QUALIFILE]
    DO
      [If using Qualifile Advantage, uncommmet this
      @CREDITREPORTSTRATEGYTYPE="CK001"]     
      @CREDITREPORTIDSTATE = UPPERCASE(LAPIDENTIDDESCRIPTION(I, 1))
      @CREDITREPORTIDNUMBER = UPPERCASE(LAPIDENTIDNUMBER(I, 1))     
    END
  END
END [PROCEDURE]

PROCEDURE GETCREDSCORE
  [Finds credit score on credit report.  This code needs to be modified based on credit bureau, 
 and what presedence each applicant score has. ]

  FOR EACH CREDREP WITH CREDREP:SSN = LAPSSN(0)
  DO
    IF CREDREP:BUREAU = 1 THEN
      [EXPERIAN V6]
    DO
      FOR EACH CREDREP ITEM WITH CREDREP ITEM:SEGMENTTYPE = "125"
      DO                                                            [FAIR ISSAC]
        IF CREDREP ITEM:CH1:2 = "F" THEN
        DO
          IF CREDREPSCORE <> 0 THEN
            SPOUSECREDREPSCORE = VALUE(CREDREP ITEM:CH4:1)
          ELSE
            CREDREPSCORE = VALUE(CREDREP ITEM:CH4:1)
        END
      END
    END
    IF CREDREP:BUREAU = 2 THEN
      [EQUIFAX V5]
    DO
      FOR EACH CREDREP ITEM WITH CREDREP ITEM:SEGMENTTYPE = "CP"
      DO                                                           [BEACON]
        IF VALUE(CREDREP ITEM:CH10:1) > 0 THEN
        DO
          IF CREDREPSCORE <> 0 THEN
            SPOUSECREDREPSCORE = VALUE(CREDREP ITEM:CH10:1)
          ELSE
            CREDREPSCORE = VALUE(CREDREP ITEM:CH10:1)
        END
      END
    END
    IF CREDREP:BUREAU = 3 THEN
      [TRANSUNION V4]
    DO
      FOR EACH CREDREP ITEM WITH (CREDREP ITEM:SEGMENTTYPE = "SC" OR
                                 CREDREP ITEM:SEGMENTTYPE = "SC01")
      DO                                                           [EMPIRICA]
        IF CREDREP:ECOAINQTYPE = "P" THEN
        DO
          IF CREDREPSCORE <> 0 THEN
            SPOUSECREDREPSCORE = VALUE(CREDREP ITEM:CH10:2)
          ELSE
            CREDREPSCORE = VALUE(CREDREP ITEM:CH10:2)
        END
        ELSE
          CREDREPSCORE = VALUE(CREDREP ITEM:CH10:2)
      END
    END
  END

  IF CREDREPSCORE <> 0 THEN
  DO
    FMPERFORM REVISE LOANAPP LAID (FMPPRIV, 0, FMPERROR)
    DO
      SET CREDITSCORE TO CREDREPSCORE
    END
  END
END [PROCEDURE]

PROCEDURE CHECKEXISTINGQFDATA
 IF @CREDITREPORTFIRSTNAME=CREDREP:FIRSTNAME AND
    @CREDITREPORTLASTNAME=CREDREP:LASTNAME AND
    @CREDITREPORTSSN=CREDREP:SSN AND
    @CREDITREPORTBIRTHDATE=CREDREP:BIRTHDATE AND                     
    @CREDITREPORTCURRADDRSTREETNAME=CREDREP:CURRADDRSTREETNAME AND
    @CREDITREPORTCURRADDRCITY=CREDREP:CURRADDRCITY AND
    @CREDITREPORTCURRADDRSTATE=CREDREP:CURRADDRSTATE AND
    @CREDITREPORTCURRADDRZIPCODE=CREDREP:CURRADDRZIPCODE AND
    @CREDITREPORTCURRADDRHOMEPHONE=CREDREP:CURRADDRHOMEPHONE THEN
  DO
   IF CREDREP:COMPLETIONDATE>=SYSACTUALDATE-30 THEN QFDATAMATCH=TRUE    
  END
END

PROCEDURE QFPULLCHECK
IF QFDATAMATCH=TRUE THEN
 DO
  DIALOGSTART ("Qualifile Re-Pull Check.",100%,0)
  DIALOGINTROTEXT("Qualifile has already been run recently or there has been no change in the applicant data.")
  DIALOGPROMPTYESNO ("Do You Wish To Run Another Qualifile?",0)
  DIALOGDISPLAY
  YESNORESPONSE=ENTERYESNO("Do You Wish To Run Another Qualifile?","0")
  DIALOGCLOSE
                                                   
  IF YESNORESPONSE="Y" THEN
   DO
    CALL INITCHEXORCREDITPULL
    PULLCREDITREPORT
   END
  ELSE POPUPMESSAGE(0,"ADDITIONAL QUALIFILE PULL CANCELLED")
 END
ELSE
 DO   
  CALL INITCHEXORCREDITPULL
  PULLCREDITREPORT
 END
END

PROCEDURE CREDREPRECHECK
 QFDATAMATCH=FALSE
 FOR EACH CREDREP 
  DO
   CALL CHECKEXISTINGQFDATA
  END
  UNTIL QFDATAMATCH=TRUE
END

[CUSTOMIZATION brhutch]
#INCLUDE "RD.BIGLISTEXPAND.PRO"
[/CUSTOMIZATION]

#INCLUDE "RD.NAME.AND.ACCOUNT.PRO"
#INCLUDE "RD.LOANAPP.PRO"
#INCLUDE "EMA.WORKFLOW.PRO.119"
#INCLUDE "EMA.JAVASCRIPTS.PRO.119"
#INCLUDE "EMA.AR.PRO.119"
