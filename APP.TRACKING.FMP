[ Updates Account, Loan, <PERSON><PERSON><PERSON> and Share tracking records] 
 FOR X=0 TO 99
  DO
   [ TRACKING ]
   FOUNDT=FALSE
   FOR EACH TRACKING WITH (TRACKING:TYPE=TTYPE(X))
    DO
     FOUNDT=TRUE
     TLOCATOR(X)=TRACKING:LOCATOR
    END
   UNTIL FOUNDT=TRUE

   IF FOUNDT=FALSE THEN
    DO
     IF TTYPE(X)=X THEN
      DO
       FMPERFORM CREATE TRACKING -1 (PRIVILEGEFLAG,TTYPE(X),NEWLOCATOR,FMPERROR)
        DO
        END
       IF FMPERROR<>"" THEN
        CALL REPORTERROR
       ELSE
        DO
         FOUNDT=TRUE
         TLOCATOR(X)=NEWLOCATOR
        END
      END
    END
   IF FOUNDT=TRUE THEN
    DO
     FMPERFORM REVISE TRACKING LOC TLOCATOR(X) (PRIVILEGEFLAG,TTYPE(X),FMPERROR)
      DO
       SET USERNUMBER1 TO TUSERNUMBER(X,1)
       SET USERNUMBER2 TO TUSERNUMBER(X,2)
       SET USERNUMBER3 TO TUSERNUMBER(X,3)
       SET USERNUMBER4 TO TUSERNUMBER(X,4)
       SET USERNUMBER5 TO TUSERNUMBER(X,5)
       SET USERNUMBER6 TO TUSERNUMBER(X,6)
       SET USERNUMBER7 TO TUSERNUMBER(X,7)
       SET USERNUMBER8 TO TUSERNUMBER(X,8)
       SET USERNUMBER9 TO TUSERNUMBER(X,9)
       SET USERNUMBER10 TO TUSERNUMBER(X,10)
       SET USERNUMBER11 TO TUSERNUMBER(X,11)
       SET USERNUMBER12 TO TUSERNUMBER(X,12)
       SET USERNUMBER13 TO TUSERNUMBER(X,13)
       SET USERNUMBER14 TO TUSERNUMBER(X,14)
       SET USERNUMBER15 TO TUSERNUMBER(X,15)
       SET USERNUMBER16 TO TUSERNUMBER(X,16)
       SET USERNUMBER17 TO TUSERNUMBER(X,17)
       SET USERNUMBER18 TO TUSERNUMBER(X,18)
       SET USERNUMBER19 TO TUSERNUMBER(X,19)
       SET USERNUMBER20 TO TUSERNUMBER(X,20)
       SET USERCHAR1 TO TUSERCHAR(X,1)
       SET USERCHAR2 TO TUSERCHAR(X,2)
       SET USERCHAR3 TO TUSERCHAR(X,3)
       SET USERCHAR4 TO TUSERCHAR(X,4)
       SET USERCHAR5 TO TUSERCHAR(X,5)
       SET USERCHAR6 TO TUSERCHAR(X,6)
       SET USERCHAR7 TO TUSERCHAR(X,7)
       SET USERCHAR8 TO TUSERCHAR(X,8)
       SET USERCHAR9 TO TUSERCHAR(X,9)
       SET USERCHAR10 TO TUSERCHAR(X,10)
       SET USERCHAR11 TO TUSERCHAR(X,11)
       SET USERCHAR12 TO TUSERCHAR(X,12)
       SET USERCHAR13 TO TUSERCHAR(X,13)
       SET USERCHAR14 TO TUSERCHAR(X,14)
       SET USERCHAR15 TO TUSERCHAR(X,15)
       SET USERCHAR16 TO TUSERCHAR(X,16)
       SET USERCHAR17 TO TUSERCHAR(X,17)
       SET USERCHAR18 TO TUSERCHAR(X,18)
       SET USERCHAR19 TO TUSERCHAR(X,19)
       SET USERCHAR20 TO TUSERCHAR(X,20)
       SET USERAMOUNT1 TO TUSERAMOUNT(X,1)
       SET USERAMOUNT2 TO TUSERAMOUNT(X,2)
       SET USERAMOUNT3 TO TUSERAMOUNT(X,3)
       SET USERAMOUNT4 TO TUSERAMOUNT(X,4)
       SET USERAMOUNT5 TO TUSERAMOUNT(X,5)
       SET USERAMOUNT6 TO TUSERAMOUNT(X,6)
       SET USERAMOUNT7 TO TUSERAMOUNT(X,7)
       SET USERAMOUNT8 TO TUSERAMOUNT(X,8)
       SET USERAMOUNT9 TO TUSERAMOUNT(X,9)
       SET USERAMOUNT10 TO TUSERAMOUNT(X,10)
       SET USERAMOUNT11 TO TUSERAMOUNT(X,11)
       SET USERAMOUNT12 TO TUSERAMOUNT(X,12)
       SET USERAMOUNT13 TO TUSERAMOUNT(X,13)
       SET USERAMOUNT14 TO TUSERAMOUNT(X,14)
       SET USERAMOUNT15 TO TUSERAMOUNT(X,15)
       SET USERAMOUNT16 TO TUSERAMOUNT(X,16)
       SET USERAMOUNT17 TO TUSERAMOUNT(X,17)
       SET USERAMOUNT18 TO TUSERAMOUNT(X,18)
       SET USERAMOUNT19 TO TUSERAMOUNT(X,19)
       SET USERAMOUNT20 TO TUSERAMOUNT(X,20)
       SET USERCODE1 TO TUSERCODE(X,1)
       SET USERCODE2 TO TUSERCODE(X,2)
       SET USERCODE3 TO TUSERCODE(X,3)
       SET USERCODE4 TO TUSERCODE(X,4)
       SET USERCODE5 TO TUSERCODE(X,5)
       SET USERCODE6 TO TUSERCODE(X,6)
       SET USERCODE7 TO TUSERCODE(X,7)
       SET USERCODE8 TO TUSERCODE(X,8)
       SET USERCODE9 TO TUSERCODE(X,9)
       SET USERCODE10 TO TUSERCODE(X,10)
       SET USERCODE11 TO TUSERCODE(X,11)
       SET USERCODE12 TO TUSERCODE(X,12)
       SET USERCODE13 TO TUSERCODE(X,13)
       SET USERCODE14 TO TUSERCODE(X,14)
       SET USERCODE15 TO TUSERCODE(X,15)
       SET USERCODE16 TO TUSERCODE(X,16)
       SET USERCODE17 TO TUSERCODE(X,17)
       SET USERCODE18 TO TUSERCODE(X,18)
       SET USERCODE19 TO TUSERCODE(X,19)
       SET USERCODE20 TO TUSERCODE(X,20)
       SET USERDATE1 TO TUSERDATE(X,1)
       SET USERDATE2 TO TUSERDATE(X,2)
       SET USERDATE3 TO TUSERDATE(X,3)
       SET USERDATE4 TO TUSERDATE(X,4)
       SET USERDATE5 TO TUSERDATE(X,5)
       SET USERDATE6 TO TUSERDATE(X,6)
       SET USERDATE7 TO TUSERDATE(X,7)
       SET USERDATE8 TO TUSERDATE(X,8)
       SET USERDATE9 TO TUSERDATE(X,9)
       SET USERDATE10 TO TUSERDATE(X,10)
       SET USERDATE11 TO TUSERDATE(X,11)
       SET USERDATE12 TO TUSERDATE(X,12)
       SET USERDATE13 TO TUSERDATE(X,13)
       SET USERDATE14 TO TUSERDATE(X,14)
       SET USERDATE15 TO TUSERDATE(X,15)
       SET USERDATE16 TO TUSERDATE(X,16)
       SET USERDATE17 TO TUSERDATE(X,17)
       SET USERDATE18 TO TUSERDATE(X,18)
       SET USERDATE19 TO TUSERDATE(X,19)
       SET USERDATE20 TO TUSERDATE(X,20)
       SET USERRATE1 TO TUSERRATE(X,1)
       SET USERRATE2 TO TUSERRATE(X,2)
       SET USERRATE3 TO TUSERRATE(X,3)
       SET USERRATE4 TO TUSERRATE(X,4)
       SET USERRATE5 TO TUSERRATE(X,5)
       SET USERRATE6 TO TUSERRATE(X,6)
       SET USERRATE7 TO TUSERRATE(X,7)
       SET USERRATE8 TO TUSERRATE(X,8)
       SET USERRATE9 TO TUSERRATE(X,9)
       SET USERRATE10 TO TUSERRATE(X,10)
       SET USERRATE11 TO TUSERRATE(X,11)
       SET USERRATE12 TO TUSERRATE(X,12)
       SET USERRATE13 TO TUSERRATE(X,13)
       SET USERRATE14 TO TUSERRATE(X,14)
       SET USERRATE15 TO TUSERRATE(X,15)
       SET USERRATE16 TO TUSERRATE(X,16)
       SET USERRATE17 TO TUSERRATE(X,17)
       SET USERRATE18 TO TUSERRATE(X,18)
       SET USERRATE19 TO TUSERRATE(X,19)
       SET USERRATE20 TO TUSERRATE(X,20)
      END
     IF FMPERROR<>"" THEN
      CALL REPORTERROR
    END

   [ LOAN TRACKING ]
   FOUNDT=FALSE
   FOR EACH LOAN WITH (LOAN:ID=LID)
    DO
     FOR EACH LOAN TRACKING WITH (LOAN TRACKING:TYPE=LTTYPE(X))
      DO
       FOUNDT=TRUE
       LTLOCATOR(X)=LOAN TRACKING:LOCATOR
      END
     UNTIL FOUNDT=TRUE
    END
  
   IF FOUNDT=FALSE THEN
    DO
     IF LTTYPE(X)=X THEN
      DO
       FMPERFORM CREATE LOAN LID TRACKING -1 (PRIVILEGEFLAG,LTTYPE(X),NEWLOCATOR,FMPERROR)
        DO
        END
       IF FMPERROR<>"" THEN
        CALL REPORTERROR
       ELSE
        DO
         FOUNDT=TRUE
         LTLOCATOR(X)=NEWLOCATOR
        END
      END
    END
   IF FOUNDT=TRUE THEN
    DO
     FMPERFORM REVISE LOAN LID TRACKING LOC LTLOCATOR(X) (PRIVILEGEFLAG,LTTYPE(X),FMPERROR)
      DO
       SET USERNUMBER1 TO LTUSERNUMBER(X,1)
       SET USERNUMBER2 TO LTUSERNUMBER(X,2)
       SET USERNUMBER3 TO LTUSERNUMBER(X,3)
       SET USERNUMBER4 TO LTUSERNUMBER(X,4)
       SET USERNUMBER5 TO LTUSERNUMBER(X,5)
       SET USERNUMBER6 TO LTUSERNUMBER(X,6)
       SET USERNUMBER7 TO LTUSERNUMBER(X,7)
       SET USERNUMBER8 TO LTUSERNUMBER(X,8)
       SET USERNUMBER9 TO LTUSERNUMBER(X,9)
       SET USERNUMBER10 TO LTUSERNUMBER(X,10)
       SET USERNUMBER11 TO LTUSERNUMBER(X,11)
       SET USERNUMBER12 TO LTUSERNUMBER(X,12)
       SET USERNUMBER13 TO LTUSERNUMBER(X,13)
       SET USERNUMBER14 TO LTUSERNUMBER(X,14)
       SET USERNUMBER15 TO LTUSERNUMBER(X,15)
       SET USERNUMBER16 TO LTUSERNUMBER(X,16)
       SET USERNUMBER17 TO LTUSERNUMBER(X,17)
       SET USERNUMBER18 TO LTUSERNUMBER(X,18)
       SET USERNUMBER19 TO LTUSERNUMBER(X,19)
       SET USERNUMBER20 TO LTUSERNUMBER(X,20)
       SET USERCHAR1 TO LTUSERCHAR(X,1)
       SET USERCHAR2 TO LTUSERCHAR(X,2)
       SET USERCHAR3 TO LTUSERCHAR(X,3)
       SET USERCHAR4 TO LTUSERCHAR(X,4)
       SET USERCHAR5 TO LTUSERCHAR(X,5)
       SET USERCHAR6 TO LTUSERCHAR(X,6)
       SET USERCHAR7 TO LTUSERCHAR(X,7)
       SET USERCHAR8 TO LTUSERCHAR(X,8)
       SET USERCHAR9 TO LTUSERCHAR(X,9)
       SET USERCHAR10 TO LTUSERCHAR(X,10)
       SET USERCHAR11 TO LTUSERCHAR(X,11)
       SET USERCHAR12 TO LTUSERCHAR(X,12)
       SET USERCHAR13 TO LTUSERCHAR(X,13)
       SET USERCHAR14 TO LTUSERCHAR(X,14)
       SET USERCHAR15 TO LTUSERCHAR(X,15)
       SET USERCHAR16 TO LTUSERCHAR(X,16)
       SET USERCHAR17 TO LTUSERCHAR(X,17)
       SET USERCHAR18 TO LTUSERCHAR(X,18)
       SET USERCHAR19 TO LTUSERCHAR(X,19)
       SET USERCHAR20 TO LTUSERCHAR(X,20)
       SET USERAMOUNT1 TO LTUSERAMOUNT(X,1)
       SET USERAMOUNT2 TO LTUSERAMOUNT(X,2)
       SET USERAMOUNT3 TO LTUSERAMOUNT(X,3)
       SET USERAMOUNT4 TO LTUSERAMOUNT(X,4)
       SET USERAMOUNT5 TO LTUSERAMOUNT(X,5)
       SET USERAMOUNT6 TO LTUSERAMOUNT(X,6)
       SET USERAMOUNT7 TO LTUSERAMOUNT(X,7)
       SET USERAMOUNT8 TO LTUSERAMOUNT(X,8)
       SET USERAMOUNT9 TO LTUSERAMOUNT(X,9)
       SET USERAMOUNT10 TO LTUSERAMOUNT(X,10)
       SET USERAMOUNT11 TO LTUSERAMOUNT(X,11)
       SET USERAMOUNT12 TO LTUSERAMOUNT(X,12)
       SET USERAMOUNT13 TO LTUSERAMOUNT(X,13)
       SET USERAMOUNT14 TO LTUSERAMOUNT(X,14)
       SET USERAMOUNT15 TO LTUSERAMOUNT(X,15)
       SET USERAMOUNT16 TO LTUSERAMOUNT(X,16)
       SET USERAMOUNT17 TO LTUSERAMOUNT(X,17)
       SET USERAMOUNT18 TO LTUSERAMOUNT(X,18)
       SET USERAMOUNT19 TO LTUSERAMOUNT(X,19)
       SET USERAMOUNT20 TO LTUSERAMOUNT(X,20)
       SET USERCODE1 TO LTUSERCODE(X,1)
       SET USERCODE2 TO LTUSERCODE(X,2)
       SET USERCODE3 TO LTUSERCODE(X,3)
       SET USERCODE4 TO LTUSERCODE(X,4)
       SET USERCODE5 TO LTUSERCODE(X,5)
       SET USERCODE6 TO LTUSERCODE(X,6)
       SET USERCODE7 TO LTUSERCODE(X,7)
       SET USERCODE8 TO LTUSERCODE(X,8)
       SET USERCODE9 TO LTUSERCODE(X,9)
       SET USERCODE10 TO LTUSERCODE(X,10)
       SET USERCODE11 TO LTUSERCODE(X,11)
       SET USERCODE12 TO LTUSERCODE(X,12)
       SET USERCODE13 TO LTUSERCODE(X,13)
       SET USERCODE14 TO LTUSERCODE(X,14)
       SET USERCODE15 TO LTUSERCODE(X,15)
       SET USERCODE16 TO LTUSERCODE(X,16)
       SET USERCODE17 TO LTUSERCODE(X,17)
       SET USERCODE18 TO LTUSERCODE(X,18)
       SET USERCODE19 TO LTUSERCODE(X,19)
       SET USERCODE20 TO LTUSERCODE(X,20)
       SET USERDATE1 TO LTUSERDATE(X,1)
       SET USERDATE2 TO LTUSERDATE(X,2)
       SET USERDATE3 TO LTUSERDATE(X,3)
       SET USERDATE4 TO LTUSERDATE(X,4)
       SET USERDATE5 TO LTUSERDATE(X,5)
       SET USERDATE6 TO LTUSERDATE(X,6)
       SET USERDATE7 TO LTUSERDATE(X,7)
       SET USERDATE8 TO LTUSERDATE(X,8)
       SET USERDATE9 TO LTUSERDATE(X,9)
       SET USERDATE10 TO LTUSERDATE(X,10)
       SET USERDATE11 TO LTUSERDATE(X,11)
       SET USERDATE12 TO LTUSERDATE(X,12)
       SET USERDATE13 TO LTUSERDATE(X,13)
       SET USERDATE14 TO LTUSERDATE(X,14)
       SET USERDATE15 TO LTUSERDATE(X,15)
       SET USERDATE16 TO LTUSERDATE(X,16)
       SET USERDATE17 TO LTUSERDATE(X,17)
       SET USERDATE18 TO LTUSERDATE(X,18)
       SET USERDATE19 TO LTUSERDATE(X,19)
       SET USERDATE20 TO LTUSERDATE(X,20)
       SET USERRATE1 TO LTUSERRATE(X,1)
       SET USERRATE2 TO LTUSERRATE(X,2)
       SET USERRATE3 TO LTUSERRATE(X,3)
       SET USERRATE4 TO LTUSERRATE(X,4)
       SET USERRATE5 TO LTUSERRATE(X,5)
       SET USERRATE6 TO LTUSERRATE(X,6)
       SET USERRATE7 TO LTUSERRATE(X,7)
       SET USERRATE8 TO LTUSERRATE(X,8)
       SET USERRATE9 TO LTUSERRATE(X,9)
       SET USERRATE10 TO LTUSERRATE(X,10)
       SET USERRATE11 TO LTUSERRATE(X,11)
       SET USERRATE12 TO LTUSERRATE(X,12)
       SET USERRATE13 TO LTUSERRATE(X,13)
       SET USERRATE14 TO LTUSERRATE(X,14)
       SET USERRATE15 TO LTUSERRATE(X,15)
       SET USERRATE16 TO LTUSERRATE(X,16)
       SET USERRATE17 TO LTUSERRATE(X,17)
       SET USERRATE18 TO LTUSERRATE(X,18)
       SET USERRATE19 TO LTUSERRATE(X,19)
       SET USERRATE20 TO LTUSERRATE(X,20)
      END
     IF FMPERROR<>"" THEN
      CALL REPORTERROR
    END

   [ LOANAPP TRACKING ]
   FOUNDT=FALSE
   FOR EACH LOANAPP WITH (LOANAPP:ID=LAID)
    DO
     FOR EACH LOANAPP TRACKING WITH (LOANAPP TRACKING:TYPE=LATTYPE(X))
      DO
       FOUNDT=TRUE
       LATLOCATOR(X)=LOANAPP TRACKING:LOCATOR
      END
     UNTIL FOUNDT=TRUE
    END
   IF FOUNDT=FALSE THEN
    DO
     IF LATTYPE(X)=X THEN
      DO
       FMPERFORM CREATE LOANAPP LAID TRACKING -1 (PRIVILEGEFLAG,LATTYPE(X),NEWLOCATOR,FMPERROR)
        DO
        END
       IF FMPERROR<>"" THEN
        CALL REPORTERROR
       ELSE
        DO
         FOUNDT=TRUE
         LATLOCATOR(X)=NEWLOCATOR
        END
      END
    END
   IF FOUNDT=TRUE THEN
    DO
     FMPERFORM REVISE LOANAPP LAID TRACKING LOC LATLOCATOR(X) (PRIVILEGEFLAG,LATTYPE(X),FMPERROR)
      DO
       SET USERNUMBER1 TO LATUSERNUMBER(X,1)
       SET USERNUMBER2 TO LATUSERNUMBER(X,2)
       SET USERNUMBER3 TO LATUSERNUMBER(X,3)
       SET USERNUMBER4 TO LATUSERNUMBER(X,4)
       SET USERNUMBER5 TO LATUSERNUMBER(X,5)
       SET USERNUMBER6 TO LATUSERNUMBER(X,6)
       SET USERNUMBER7 TO LATUSERNUMBER(X,7)
       SET USERNUMBER8 TO LATUSERNUMBER(X,8)
       SET USERNUMBER9 TO LATUSERNUMBER(X,9)
       SET USERNUMBER10 TO LATUSERNUMBER(X,10)
       SET USERNUMBER11 TO LATUSERNUMBER(X,11)
       SET USERNUMBER12 TO LATUSERNUMBER(X,12)
       SET USERNUMBER13 TO LATUSERNUMBER(X,13)
       SET USERNUMBER14 TO LATUSERNUMBER(X,14)
       SET USERNUMBER15 TO LATUSERNUMBER(X,15)
       SET USERNUMBER16 TO LATUSERNUMBER(X,16)
       SET USERNUMBER17 TO LATUSERNUMBER(X,17)
       SET USERNUMBER18 TO LATUSERNUMBER(X,18)
       SET USERNUMBER19 TO LATUSERNUMBER(X,19)
       SET USERNUMBER20 TO LATUSERNUMBER(X,20)
       SET USERCHAR1 TO LATUSERCHAR(X,1)
       SET USERCHAR2 TO LATUSERCHAR(X,2)
       SET USERCHAR3 TO LATUSERCHAR(X,3)
       SET USERCHAR4 TO LATUSERCHAR(X,4)
       SET USERCHAR5 TO LATUSERCHAR(X,5)
       SET USERCHAR6 TO LATUSERCHAR(X,6)
       SET USERCHAR7 TO LATUSERCHAR(X,7)
       SET USERCHAR8 TO LATUSERCHAR(X,8)
       SET USERCHAR9 TO LATUSERCHAR(X,9)
       SET USERCHAR10 TO LATUSERCHAR(X,10)
       SET USERCHAR11 TO LATUSERCHAR(X,11)
       SET USERCHAR12 TO LATUSERCHAR(X,12)
       SET USERCHAR13 TO LATUSERCHAR(X,13)
       SET USERCHAR14 TO LATUSERCHAR(X,14)
       SET USERCHAR15 TO LATUSERCHAR(X,15)
       SET USERCHAR16 TO LATUSERCHAR(X,16)
       SET USERCHAR17 TO LATUSERCHAR(X,17)
       SET USERCHAR18 TO LATUSERCHAR(X,18)
       SET USERCHAR19 TO LATUSERCHAR(X,19)
       SET USERCHAR20 TO LATUSERCHAR(X,20)
       SET USERAMOUNT1 TO LATUSERAMOUNT(X,1)
       SET USERAMOUNT2 TO LATUSERAMOUNT(X,2)
       SET USERAMOUNT3 TO LATUSERAMOUNT(X,3)
       SET USERAMOUNT4 TO LATUSERAMOUNT(X,4)
       SET USERAMOUNT5 TO LATUSERAMOUNT(X,5)
       SET USERAMOUNT6 TO LATUSERAMOUNT(X,6)
       SET USERAMOUNT7 TO LATUSERAMOUNT(X,7)
       SET USERAMOUNT8 TO LATUSERAMOUNT(X,8)
       SET USERAMOUNT9 TO LATUSERAMOUNT(X,9)
       SET USERAMOUNT10 TO LATUSERAMOUNT(X,10)
       SET USERAMOUNT11 TO LATUSERAMOUNT(X,11)
       SET USERAMOUNT12 TO LATUSERAMOUNT(X,12)
       SET USERAMOUNT13 TO LATUSERAMOUNT(X,13)
       SET USERAMOUNT14 TO LATUSERAMOUNT(X,14)
       SET USERAMOUNT15 TO LATUSERAMOUNT(X,15)
       SET USERAMOUNT16 TO LATUSERAMOUNT(X,16)
       SET USERAMOUNT17 TO LATUSERAMOUNT(X,17)
       SET USERAMOUNT18 TO LATUSERAMOUNT(X,18)
       SET USERAMOUNT19 TO LATUSERAMOUNT(X,19)
       SET USERAMOUNT20 TO LATUSERAMOUNT(X,20)
       SET USERCODE1 TO LATUSERCODE(X,1)
       SET USERCODE2 TO LATUSERCODE(X,2)
       SET USERCODE3 TO LATUSERCODE(X,3)
       SET USERCODE4 TO LATUSERCODE(X,4)
       SET USERCODE5 TO LATUSERCODE(X,5)
       SET USERCODE6 TO LATUSERCODE(X,6)
       SET USERCODE7 TO LATUSERCODE(X,7)
       SET USERCODE8 TO LATUSERCODE(X,8)
       SET USERCODE9 TO LATUSERCODE(X,9)
       SET USERCODE10 TO LATUSERCODE(X,10)
       SET USERCODE11 TO LATUSERCODE(X,11)
       SET USERCODE12 TO LATUSERCODE(X,12)
       SET USERCODE13 TO LATUSERCODE(X,13)
       SET USERCODE14 TO LATUSERCODE(X,14)
       SET USERCODE15 TO LATUSERCODE(X,15)
       SET USERCODE16 TO LATUSERCODE(X,16)
       SET USERCODE17 TO LATUSERCODE(X,17)
       SET USERCODE18 TO LATUSERCODE(X,18)
       SET USERCODE19 TO LATUSERCODE(X,19)
       SET USERCODE20 TO LATUSERCODE(X,20)
       SET USERDATE1 TO LATUSERDATE(X,1)
       SET USERDATE2 TO LATUSERDATE(X,2)
       SET USERDATE3 TO LATUSERDATE(X,3)
       SET USERDATE4 TO LATUSERDATE(X,4)
       SET USERDATE5 TO LATUSERDATE(X,5)
       SET USERDATE6 TO LATUSERDATE(X,6)
       SET USERDATE7 TO LATUSERDATE(X,7)
       SET USERDATE8 TO LATUSERDATE(X,8)
       SET USERDATE9 TO LATUSERDATE(X,9)
       SET USERDATE10 TO LATUSERDATE(X,10)
       SET USERDATE11 TO LATUSERDATE(X,11)
       SET USERDATE12 TO LATUSERDATE(X,12)
       SET USERDATE13 TO LATUSERDATE(X,13)
       SET USERDATE14 TO LATUSERDATE(X,14)
       SET USERDATE15 TO LATUSERDATE(X,15)
       SET USERDATE16 TO LATUSERDATE(X,16)
       SET USERDATE17 TO LATUSERDATE(X,17)
       SET USERDATE18 TO LATUSERDATE(X,18)
       SET USERDATE19 TO LATUSERDATE(X,19)
       SET USERDATE20 TO LATUSERDATE(X,20)
       SET USERRATE1 TO LATUSERRATE(X,1)
       SET USERRATE2 TO LATUSERRATE(X,2)
       SET USERRATE3 TO LATUSERRATE(X,3)
       SET USERRATE4 TO LATUSERRATE(X,4)
       SET USERRATE5 TO LATUSERRATE(X,5)
       SET USERRATE6 TO LATUSERRATE(X,6)
       SET USERRATE7 TO LATUSERRATE(X,7)
       SET USERRATE8 TO LATUSERRATE(X,8)
       SET USERRATE9 TO LATUSERRATE(X,9)
       SET USERRATE10 TO LATUSERRATE(X,10)
       SET USERRATE11 TO LATUSERRATE(X,11)
       SET USERRATE12 TO LATUSERRATE(X,12)
       SET USERRATE13 TO LATUSERRATE(X,13)
       SET USERRATE14 TO LATUSERRATE(X,14)
       SET USERRATE15 TO LATUSERRATE(X,15)
       SET USERRATE16 TO LATUSERRATE(X,16)
       SET USERRATE17 TO LATUSERRATE(X,17)
       SET USERRATE18 TO LATUSERRATE(X,18)
       SET USERRATE19 TO LATUSERRATE(X,19)
       SET USERRATE20 TO LATUSERRATE(X,20)
      END
     IF FMPERROR<>"" THEN
      CALL REPORTERROR
    END

   [ SHARE TRACKING ]
   FOUNDT=FALSE
   FOR EACH SHARE WITH (SHARE:ID=SID)
    DO
     FOR EACH SHARE TRACKING WITH (SHARE TRACKING:TYPE=STTYPE(X))
      DO
       FOUNDT=TRUE
       STLOCATOR(X)=SHARE TRACKING:LOCATOR
      END
     UNTIL FOUNDT=TRUE
    END
   IF FOUNDT=FALSE THEN
    DO
     IF STTYPE(X)=X THEN
      DO
       FMPERFORM CREATE SHARE SID TRACKING -1 (PRIVILEGEFLAG,STTYPE(X),NEWLOCATOR,FMPERROR)
        DO
        END
       IF FMPERROR<>"" THEN
        CALL REPORTERROR
       ELSE
        DO
         FOUNDT=TRUE
         STLOCATOR(X)=NEWLOCATOR
        END
      END
    END
   IF FOUNDT=TRUE THEN
    DO
     FMPERFORM REVISE SHARE SID TRACKING LOC STLOCATOR(X) (PRIVILEGEFLAG,STTYPE(X),FMPERROR)
      DO
       SET USERNUMBER1 TO STUSERNUMBER(X,1)
       SET USERNUMBER2 TO STUSERNUMBER(X,2)
       SET USERNUMBER3 TO STUSERNUMBER(X,3)
       SET USERNUMBER4 TO STUSERNUMBER(X,4)
       SET USERNUMBER5 TO STUSERNUMBER(X,5)
       SET USERNUMBER6 TO STUSERNUMBER(X,6)
       SET USERNUMBER7 TO STUSERNUMBER(X,7)
       SET USERNUMBER8 TO STUSERNUMBER(X,8)
       SET USERNUMBER9 TO STUSERNUMBER(X,9)
       SET USERNUMBER10 TO STUSERNUMBER(X,10)
       SET USERNUMBER11 TO STUSERNUMBER(X,11)
       SET USERNUMBER12 TO STUSERNUMBER(X,12)
       SET USERNUMBER13 TO STUSERNUMBER(X,13)
       SET USERNUMBER14 TO STUSERNUMBER(X,14)
       SET USERNUMBER15 TO STUSERNUMBER(X,15)
       SET USERNUMBER16 TO STUSERNUMBER(X,16)
       SET USERNUMBER17 TO STUSERNUMBER(X,17)
       SET USERNUMBER18 TO STUSERNUMBER(X,18)
       SET USERNUMBER19 TO STUSERNUMBER(X,19)
       SET USERNUMBER20 TO STUSERNUMBER(X,20)
       SET USERCHAR1 TO STUSERCHAR(X,1)
       SET USERCHAR2 TO STUSERCHAR(X,2)
       SET USERCHAR3 TO STUSERCHAR(X,3)
       SET USERCHAR4 TO STUSERCHAR(X,4)
       SET USERCHAR5 TO STUSERCHAR(X,5)
       SET USERCHAR6 TO STUSERCHAR(X,6)
       SET USERCHAR7 TO STUSERCHAR(X,7)
       SET USERCHAR8 TO STUSERCHAR(X,8)
       SET USERCHAR9 TO STUSERCHAR(X,9)
       SET USERCHAR10 TO STUSERCHAR(X,10)
       SET USERCHAR11 TO STUSERCHAR(X,11)
       SET USERCHAR12 TO STUSERCHAR(X,12)
       SET USERCHAR13 TO STUSERCHAR(X,13)
       SET USERCHAR14 TO STUSERCHAR(X,14)
       SET USERCHAR15 TO STUSERCHAR(X,15)
       SET USERCHAR16 TO STUSERCHAR(X,16)
       SET USERCHAR17 TO STUSERCHAR(X,17)
       SET USERCHAR18 TO STUSERCHAR(X,18)
       SET USERCHAR19 TO STUSERCHAR(X,19)
       SET USERCHAR20 TO STUSERCHAR(X,20)
       SET USERAMOUNT1 TO STUSERAMOUNT(X,1)
       SET USERAMOUNT2 TO STUSERAMOUNT(X,2)
       SET USERAMOUNT3 TO STUSERAMOUNT(X,3)
       SET USERAMOUNT4 TO STUSERAMOUNT(X,4)
       SET USERAMOUNT5 TO STUSERAMOUNT(X,5)
       SET USERAMOUNT6 TO STUSERAMOUNT(X,6)
       SET USERAMOUNT7 TO STUSERAMOUNT(X,7)
       SET USERAMOUNT8 TO STUSERAMOUNT(X,8)
       SET USERAMOUNT9 TO STUSERAMOUNT(X,9)
       SET USERAMOUNT10 TO STUSERAMOUNT(X,10)
       SET USERAMOUNT11 TO STUSERAMOUNT(X,11)
       SET USERAMOUNT12 TO STUSERAMOUNT(X,12)
       SET USERAMOUNT13 TO STUSERAMOUNT(X,13)
       SET USERAMOUNT14 TO STUSERAMOUNT(X,14)
       SET USERAMOUNT15 TO STUSERAMOUNT(X,15)
       SET USERAMOUNT16 TO STUSERAMOUNT(X,16)
       SET USERAMOUNT17 TO STUSERAMOUNT(X,17)
       SET USERAMOUNT18 TO STUSERAMOUNT(X,18)
       SET USERAMOUNT19 TO STUSERAMOUNT(X,19)
       SET USERAMOUNT20 TO STUSERAMOUNT(X,20)
       SET USERCODE1 TO STUSERCODE(X,1)
       SET USERCODE2 TO STUSERCODE(X,2)
       SET USERCODE3 TO STUSERCODE(X,3)
       SET USERCODE4 TO STUSERCODE(X,4)
       SET USERCODE5 TO STUSERCODE(X,5)
       SET USERCODE6 TO STUSERCODE(X,6)
       SET USERCODE7 TO STUSERCODE(X,7)
       SET USERCODE8 TO STUSERCODE(X,8)
       SET USERCODE9 TO STUSERCODE(X,9)
       SET USERCODE10 TO STUSERCODE(X,10)
       SET USERCODE11 TO STUSERCODE(X,11)
       SET USERCODE12 TO STUSERCODE(X,12)
       SET USERCODE13 TO STUSERCODE(X,13)
       SET USERCODE14 TO STUSERCODE(X,14)
       SET USERCODE15 TO STUSERCODE(X,15)
       SET USERCODE16 TO STUSERCODE(X,16)
       SET USERCODE17 TO STUSERCODE(X,17)
       SET USERCODE18 TO STUSERCODE(X,18)
       SET USERCODE19 TO STUSERCODE(X,19)
       SET USERCODE20 TO STUSERCODE(X,20)
       SET USERDATE1 TO STUSERDATE(X,1)
       SET USERDATE2 TO STUSERDATE(X,2)
       SET USERDATE3 TO STUSERDATE(X,3)
       SET USERDATE4 TO STUSERDATE(X,4)
       SET USERDATE5 TO STUSERDATE(X,5)
       SET USERDATE6 TO STUSERDATE(X,6)
       SET USERDATE7 TO STUSERDATE(X,7)
       SET USERDATE8 TO STUSERDATE(X,8)
       SET USERDATE9 TO STUSERDATE(X,9)
       SET USERDATE10 TO STUSERDATE(X,10)
       SET USERDATE11 TO STUSERDATE(X,11)
       SET USERDATE12 TO STUSERDATE(X,12)
       SET USERDATE13 TO STUSERDATE(X,13)
       SET USERDATE14 TO STUSERDATE(X,14)
       SET USERDATE15 TO STUSERDATE(X,15)
       SET USERDATE16 TO STUSERDATE(X,16)
       SET USERDATE17 TO STUSERDATE(X,17)
       SET USERDATE18 TO STUSERDATE(X,18)
       SET USERDATE19 TO STUSERDATE(X,19)
       SET USERDATE20 TO STUSERDATE(X,20)
       SET USERRATE1 TO STUSERRATE(X,1)
       SET USERRATE2 TO STUSERRATE(X,2)
       SET USERRATE3 TO STUSERRATE(X,3)
       SET USERRATE4 TO STUSERRATE(X,4)
       SET USERRATE5 TO STUSERRATE(X,5)
       SET USERRATE6 TO STUSERRATE(X,6)
       SET USERRATE7 TO STUSERRATE(X,7)
       SET USERRATE8 TO STUSERRATE(X,8)
       SET USERRATE9 TO STUSERRATE(X,9)
       SET USERRATE10 TO STUSERRATE(X,10)
       SET USERRATE11 TO STUSERRATE(X,11)
       SET USERRATE12 TO STUSERRATE(X,12)
       SET USERRATE13 TO STUSERRATE(X,13)
       SET USERRATE14 TO STUSERRATE(X,14)
       SET USERRATE15 TO STUSERRATE(X,15)
       SET USERRATE16 TO STUSERRATE(X,16)
       SET USERRATE17 TO STUSERRATE(X,17)
       SET USERRATE18 TO STUSERRATE(X,18)
       SET USERRATE19 TO STUSERRATE(X,19)
       SET USERRATE20 TO STUSERRATE(X,20)
      END
     IF FMPERROR<>"" THEN
      CALL REPORTERROR
    END
  END
