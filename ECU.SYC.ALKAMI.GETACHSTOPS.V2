[Repgen: ECU.SYC.ALKAMI.ACHSTOPPAY
Author: <PERSON>
Creation Date: 1/1/2023
Description: 
Notes: Called by <PERSON><PERSON><PERSON>K widget ACH Stop Pay. Gets current Stop ACH records
History:
 JDH 07/24/2023 - Fix ampersand issue
 JDH 07/26/2023 - Add hold type 16 to the list of current stops
]
SYMCONNECT
STATELESS

TARGET=ACCOUNT

DEFINE
 THISACCT=CHARACTER
 REQUESTORSSN=CHARACTER
 TRUE=1
 FALSE=0
 
 FOUND=NUMBER
 I=NUMBER
 X=NUMBER
 OURACCOUNTS = CHARACTER(10) ARRAY(50)
 NUMBEROFACCESS=NUMBER
 DEBUGTEXT=CHARACTER 
 SHOWTHISACCOUNT=NUMBER
 ELIGNAMETYPE=NUMBER ARRAY(30)
 
 [NOTE RECORDS] 
 ACHNOTECODE=36
 NOTECODECNT=NUMBER
 NOTETEXT1=CHARACTER ARRAY(500)
 NOTETEXT2=CHARACTER ARRAY(500)
 NOTETEXT3=CHARACTER ARRAY(500)
 NOTETEXT4=CHARACTER ARRAY(500)
 NOTETEXT5=CHARACTER ARRAY(500)
 NOTECREATEDATE=CHARACTER ARRAY(500)
 NOTECREATETIME=CHARACTER ARRAY(500)
 
 DEBUGMODE=FALSE
 FNUMBER=NUMBER
 FERROR=CHARACTER
 DELIM=CHARACTER
 
 [Debug logging variables]
 LOGINDENT=CHARACTER
 LOGDATE=CHARACTER
 LOGTIME=CHARACTER
 
 #INCLUDE "ECU.FORMATDATA.V2.DEF"
END

PRINT TITLE="ECU.SYC.ALKAMI.ACHSTOPPAY" 
 IF DEBUGMODE=FALSE OR SYSSYMDIRECTORY=0 [make sure no debug in prod] THEN
  DO
   THISACCT=@RGUSERCHR1
   REQUESTORSSN=@RGUSERCHR2
  END
  ELSE
   DO
    REQUESTORSSN=NAME:SSN
   END
   
 IF THISACCT="" THEN
   THISACCT=ACCOUNT:NUMBER
   
 CALL CONFIGURE
 CALL INITLOG
 
 DEBUGTEXT="Starting GETLINKEDACCOUNTS"
 CALL LOGDEBUG
 CALL GETLINKEDACCOUNTS
 
 X = 0
 
 PRINT "---HOLDS---" NEWLINE
 
 WHILE X < NUMBEROFACCESS
  DO
   X = X + 1
   IF OURACCOUNTS(X) <> "" THEN
    DO
     SHOWTHISACCOUNT = FALSE
     FOR ACCOUNT OURACCOUNTS(X)
      DO
       DEBUGTEXT="Processing account: "+OURACCOUNTS(X)
       CALL LOGDEBUG
       
       FOR EACH NAME
        DO
          IF ELIGNAMETYPE(NAME:TYPE)=TRUE
             AND NAME:SSN = REQUESTORSSN
             AND NAME:EXPIRATIONDATE = '--/--/--'
             AND NAME:DEATHDATE = '--/--/--' THEN
          DO
            SHOWTHISACCOUNT = TRUE
            DEBUGTEXT="Found eligible name type: " + FORMAT("99", NAME:TYPE)
            CALL LOGDEBUG
          END
        END UNTIL SHOWTHISACCOUNT = TRUE
        
        IF SHOWTHISACCOUNT = TRUE THEN
         DO
          DEBUGTEXT="Account "+OURACCOUNTS(X)+" is eligible for display"
          CALL LOGDEBUG          
          CALL PRINTACHSTOPS
          CALL GETSTOPNOTES
         END
      END
    END
  END
 
 [Delimiter line to separate holds from notes]
 PRINT "---STOPNOTES---" NEWLINE
 CALL PRINTSTOPNOTES
 NEWLINE
END

PROCEDURE CONFIGURE
 ELIGNAMETYPE(0)=TRUE
 ELIGNAMETYPE(1)=TRUE
 DELIM="|"  [Set delimiter to pipe character]
END

PROCEDURE GETLINKEDACCOUNTS
 [Find the other accounts it can access]
  IF REQUESTORSSN <> "" THEN
  DO
    FOR EACH PREFERENCE WITH PREFERENCE:TYPE = 0
    DO
      FOR EACH PREFERENCE ACCESS WITH PREFERENCE ACCESS:ACCESSTYPE = 2
      DO
        FOUND=FALSE
        FOR I=0 TO NUMBEROFACCESS
        DO
          IF OURACCOUNTS(I) = PREFERENCE ACCESS:ACCOUNTNUMBER THEN
            FOUND=TRUE
        END
           
        IF FOUND=FALSE THEN
        DO
          NUMBEROFACCESS = NUMBEROFACCESS + 1
          OURACCOUNTS(NUMBEROFACCESS) = PREFERENCE ACCESS:ACCOUNTNUMBER
	     
          DEBUGTEXT="OURACCOUNTS(" + FORMAT("99",NUMBEROFACCESS) + ")=" +  OURACCOUNTS(NUMBEROFACCESS)
          CALL LOGDEBUG
        END
      END
    END UNTIL PREFERENCE:TYPE = 0
  END
  
  [Every account will have access to itself]
  NUMBEROFACCESS = NUMBEROFACCESS + 1
  OURACCOUNTS(NUMBEROFACCESS) = ACCOUNT:NUMBER
END

[Stops will be pipe-delimited in the following format:
AccountNumber|Type|AchRecurringStop|EffectiveDate|ExpirationDate|ExpirationTime|HoldCreationDate|HoldCreationTime|MatchTime|Amount|Reference1|PayeeName|Locator|ID|Description

A delimiter line "---STOPNOTES---" separates the holds from the notes.
]
PROCEDURE PRINTACHSTOPS
 DEBUGTEXT="Searching for ACH stops in account: "+ACCOUNT:NUMBER
 CALL LOGDEBUG
 
 FOR EACH SHARE
  DO
   FOR EACH SHARE HOLD WITH
            (SHARE HOLD:TYPE = 6 OR SHARE HOLD:TYPE = 16) AND
            SHARE HOLD:MATCHDATE      = '--/--/--'      AND
           (SHARE HOLD:EXPIRATIONDATE = '--/--/--'      OR
            SHARE HOLD:EXPIRATIONDATE > SYSACTUALDATE   OR
            (SHARE HOLD:EXPIRATIONDATE = SYSACTUALDATE   AND
             SHARE HOLD:EXPIRATIONTIME > SYSACTUALTIME))
    DO
     PRINT ACCOUNT:NUMBER
     PRINT DELIM
     PRINT FORMAT("99",SHARE HOLD:TYPE)
     PRINT DELIM
     PRINT FORMAT("99",SHARE HOLD:ACHRECURRINGSTOP)
     PRINT DELIM
     PRINT FORMAT("99/99/9999",SHARE HOLD:EFFECTIVEDATE)
     PRINT DELIM
     PRINT FORMAT("99/99/9999",SHARE HOLD:EXPIRATIONDATE)
     PRINT DELIM
     PRINT FORMAT("9999",SHARE HOLD:EXPIRATIONTIME)
     PRINT DELIM
     PRINT FORMAT("99/99/9999",SHARE HOLD:HOLDCREATIONDATE)
     PRINT DELIM
     PRINT SHARE HOLD:HOLDCREATIONTIME
     PRINT DELIM
     PRINT FORMAT("9999",SHARE HOLD:MATCHTIME)
     PRINT DELIM
     
     ECUTEMPCHARIN=FORMAT("########9.99",SHARE HOLD:AMOUNT)
     CALL ECUREMOVELEADINGSPACES
     PRINT ECUTEMPCHAROUT
     PRINT DELIM
     
     PRINT SHARE HOLD:REFERENCE1
     PRINT DELIM
     
     ECUTEMPCHARIN=SHARE HOLD:PAYEENAME
     CALL ECUREPLACEAMPERSAND
     PRINT ECUTEMPCHAROUT
     PRINT DELIM
     
     PRINT FORMAT("######9",SHARE HOLD:LOCATOR)
     PRINT DELIM
     PRINT SHARE:ID
     PRINT DELIM
     PRINT SHARE:DESCRIPTION
     NEWLINE
     
     DEBUGTEXT="Found ACH stop: "+SHARE HOLD:PAYEENAME+", Amount: "+FORMAT("########9.99",SHARE HOLD:AMOUNT)+", Type: "+FORMAT("99",SHARE HOLD:TYPE)
     CALL LOGDEBUG
    END
  END
  
  DEBUGTEXT="Completed ACH stop search for account: "+ACCOUNT:NUMBER
  CALL LOGDEBUG
END

PROCEDURE GETSTOPNOTES
 DEBUGTEXT="Searching for stop notes in account: "+ACCOUNT:NUMBER
 CALL LOGDEBUG
 
 FOR EACH SHARE
  DO
   FOR EACH SHARE NOTE WITH SHARE NOTE:CODE=ACHNOTECODE
    DO
     NOTECODECNT=NOTECODECNT+1
     NOTETEXT1(NOTECODECNT)=SHARE NOTE:TEXT:1
     NOTETEXT2(NOTECODECNT)=SHARE NOTE:TEXT:2
     NOTETEXT3(NOTECODECNT)=SHARE NOTE:TEXT:3
     NOTETEXT4(NOTECODECNT)=SHARE NOTE:TEXT:4
     ECUTEMPCHARIN=SHARE NOTE:TEXT:5
     CALL ECUREPLACEAMPERSAND      
     NOTETEXT5(NOTECODECNT)=ECUTEMPCHAROUT
     NOTECREATEDATE(NOTECODECNT)=FORMAT("99/99/99",SHARE NOTE:ENTERDATE)
     NOTECREATETIME(NOTECODECNT)=FORMAT("9999",SHARE NOTE:ENTERTIME)
     
     DEBUGTEXT="Found stop note: "+NOTETEXT1(NOTECODECNT)+" - "+NOTETEXT2(NOTECODECNT)
     CALL LOGDEBUG
    END
  END
  
  DEBUGTEXT="Found "+FORMAT("99",NOTECODECNT)+" stop notes in account: "+ACCOUNT:NUMBER
  CALL LOGDEBUG
END

[Pipe-delimited format for stop notes:
Text1|Text2|Text3|Text4|Text5|CreateDate|CreateTime
]
PROCEDURE PRINTSTOPNOTES
 DEBUGTEXT="Outputting "+FORMAT("99",NOTECODECNT)+" stop notes"
 CALL LOGDEBUG
 
 FOR X=1 TO NOTECODECNT
  DO
   PRINT NOTETEXT1(X)
   PRINT DELIM
   PRINT NOTETEXT2(X)
   PRINT DELIM
   PRINT NOTETEXT3(X)
   PRINT DELIM
   PRINT NOTETEXT4(X)
   PRINT DELIM
   PRINT NOTETEXT5(X)
   PRINT DELIM
   PRINT NOTECREATEDATE(X)
   PRINT DELIM
   PRINT NOTECREATETIME(X)
   NEWLINE
  END
END

PROCEDURE INITLOG
  [Initialize log file with header]
  LOGDATE=FORMAT("99/99/9999", SYSACTUALDATE)
  LOGTIME=FORMAT("99:99", SYSACTUALTIME)
  LOGINDENT="  "
  
  IF DEBUGMODE=TRUE THEN
  DO
    FILEOPEN("LETTER","SYC.GETACHSTOPS.log","APPEND",FNUMBER,FERROR)
    FILEWRITELINE(FNUMBER,"==== START ACHSTOPPAY "+LOGDATE+" "+LOGTIME+" ====",FERROR)
    FILEWRITELINE(FNUMBER,LOGINDENT+"Account: "+THISACCT,FERROR)
    FILEWRITELINE(FNUMBER,LOGINDENT+"Requestor SSN: "+REQUESTORSSN,FERROR)
    FILECLOSE(FNUMBER,FERROR)
  END
END

PROCEDURE LOGDEBUG
  [Log debug message]
  IF DEBUGMODE=TRUE THEN
  DO
    FILEOPEN("LETTER","SYC.GETACHSTOPS.log","APPEND",FNUMBER,FERROR)
    FILEWRITELINE(FNUMBER,LOGINDENT+DEBUGTEXT,FERROR)
    FILECLOSE(FNUMBER,FERROR)
  END
END

#INCLUDE "ECU.FORMATDATA.V2.PRO"





