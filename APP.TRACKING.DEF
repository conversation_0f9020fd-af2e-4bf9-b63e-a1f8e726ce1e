[ To be used with APP.TRACKING.FMP and APP.TRACKING.PRO specfiles.
  Will support TRACKING, LOAN TRACKING, LOANAPP TRACKING, and SHARE TRACKING fields.
  Will NOT support multiple Tracking records with the same Type.

  Example:
  To create a Tracking record with Type 31, set
    TTYPE(31)=31
    #INCLUDE "APP.TRACKING.FMP"
  To create a Loan Tracking record with Type 40, set
    LTTYPE(40)=40
    #INCLUDE "APP.TRACKING.FMP"
  To create a Loan app Tracking record with Type 50, set
    LATTYPE(50)=50
    #INCLUDE "APP.TRACKING.FMP"
  To create a Share Tracking record with Type 60, set
    STTYPE(60)=60
    #INCLUDE "APP.TRACKING.FMP"
]

  FOUNDT=NUMBER
  NEWLOCATOR=NUMBER

  TTYPE=NUMBER ARRAY(100)
  TLOCATOR=NUMBER ARRAY(100)
  LTTYPE=NUMBER ARRAY(100)
  LTLOCATOR=NUMBER ARRAY(100)
  LATTYPE=NUMBER ARRAY(100)
  LATLOCATOR=NUMBER ARRAY(100)
  STTYPE=NUMBER ARRAY(100)
  STLOCATOR=NUMBER ARRAY(100)

  TUSERNUMBER=NUMBER ARRAY(100,20)
  TUSERCHAR=CHARACTER(40) ARRAY(100,20)
  TUSERAMOUNT=MONEY ARRAY(100,20)
  TUSERCODE=NUMBER ARRAY(100,20)
  TUSERDATE=DATE ARRAY(100,20)
  TUSERRATE=RATE ARRAY(100,20)
  LTUSERNUMBER=NUMBER ARRAY(100,20)
  LTUSERCHAR=CHARACTER(40) ARRAY(100,20)
  LTUSERAMOUNT=MONEY ARRAY(100,20)
  LTUSERCODE=NUMBER ARRAY(100,20)
  LTUSERDATE=DATE ARRAY(100,20)
  LTUSERRATE=RATE ARRAY(100,20)
  LATUSERNUMBER=NUMBER ARRAY(100,20)
  LATUSERCHAR=CHARACTER(40) ARRAY(100,20)
  LATUSERAMOUNT=MONEY ARRAY(100,20)
  LATUSERCODE=NUMBER ARRAY(100,20)
  LATUSERDATE=DATE ARRAY(100,20)
  LATUSERRATE=RATE ARRAY(100,20)
  STUSERNUMBER=NUMBER ARRAY(100,20)
  STUSERCHAR=CHARACTER(40) ARRAY(100,20)
  STUSERAMOUNT=MONEY ARRAY(100,20)
  STUSERCODE=NUMBER ARRAY(100,20)
  STUSERDATE=DATE ARRAY(100,20)
  STUSERRATE=RATE ARRAY(100,20)

  ATFMPERROR=NUMBER
