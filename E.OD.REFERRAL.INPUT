[E.OD.REFERRAL.INPUT
|
| This demand PowerOn specfile prompts the user for a single character code,
| validates it, and processes the code if valid. 
|
| Created By: <PERSON>
| Created On: 06/05/2025
|
|
| Dependencies:
|  None
]

SUBROUTINE DEMAND
WINDOWS

TARGET=ACCOUNT

DEFINE
 #INCLUDE "RD.GETDATA.DEF"
 TRUE=1
 FALSE=0
 
 CODEFOUND=NUMBER
 USERCODE=CHARACTER
 USERCANCELLED=NUMBER
 REFEREEACCT=CHARACTER
 ERRORTEXT=CHARACTER
 ALLOWEDTOCONTINUE=NUMBER
 X=NUMBER
 RESTRICTEDACCT=CHARACTER ARRAY(20)
END

SETUP
 ALLOWEDTOCONTINUE=TRUE
 CALL ALLOWEDCHECK
 [test alloweduser=true]
 IF ALLOWEDTOCONTINUE=TRUE THEN
  DO   
   [ Initialize variables ]
   CODEFOUND=FALSE
   USERCANCELLED=FALSE
   USERCODE=""
   
   [ Main processing loop - continue until code found or user cancels ]
   WHILE CODEFOUND=FALSE AND USERCANCELLED=FALSE
    DO
     CALL PROMPTFORCODE
     
     IF USERCANCELLED=FALSE THEN
      DO
       CALL VALIDATECODE
       
       IF CODEFOUND=TRUE THEN
        DO
         CALL PROCESSCODE
         CALL SHOWSUCCESSDIALOG
        END
       ELSE
        DO
         CALL SHOWERRORDIALOG
        END
      END
    END
  END
END

PRINT TITLE="Code Validator"
 SUPPRESSNEWLINE
END

PROCEDURE ALLOWEDCHECK
 IF (SHARE:TYPE=00 AND SHARE:OPENDATE<SYSTEMDATE-10) THEN
  DO
   ALLOWEDTOCONTINUE=FALSE
   POPUPMESSAGE(1,"REFFERAL NOT ALLOWED. Prime Share must not be older than 10 days.")
  END  
END

PROCEDURE PROMPTFORCODE
[
 Display dialog to prompt user for single character code input.
 Provides cancel functionality.
]
 
 DIALOGSTART("Referral Code",100%,0)
 DIALOGINTROTEXT("Please enter the referral code provided by the member:")
 DIALOGPROMPTCHAR("Code",8,USERCODE)
 DIALOGDISPLAY
 
 [ Get user input ]
 USERCODE=UPPERCASE(ENTERCHARACTER("Code",8,USERCODE))
 
 [ Check if user cancelled (empty input typically indicates cancel) ]
 IF USERCODE="" THEN
  USERCANCELLED=TRUE
 
 FOR X=1 to 20
  DO
   RESTRICTEDACCT(X)=GETDATACHAR(GETUSERRESTRICTEDACCOUNT,SYSUSERNUMBER,X)     
  END
  
 DIALOGCLOSE
END

PROCEDURE VALIDATECODE 
 CODEFOUND=FALSE
 IF USERCODE<>"" THEN
  DO
   FOR ACCOUNT WITH LOOKUP USERCODE
    DO
     FOR X=1 TO 20
      DO
       IF ACCOUNT:NUMBER=RESTRICTEDACCT(X) THEN
        DO
         [TODO: BLOCK USER RESTRICTED ACCOUNTS]
        END
      END
     IF LOOKUP:TYPE=10 THEN
      DO
       CODEFOUND=TRUE
       REFEREEACCT=ACCOUNT:NUMBER
      END
    END
  END
 ELSE
  CODEFOUND=FALSE
END

PROCEDURE PROCESSCODE
 FMPERFORM CREATE NOTE 0 (0,3,ERRORTEXT)
  DO
   SET EXPIRATIONDATE TO '12/31/2070'
   SET TEXT:1 TO "EMA Referral Code:"
   SET TEXT:2 TO USERCODE
  END
END

PROCEDURE SHOWERRORDIALOG 
 DIALOGSTART("Invalid Code",100%,0)
 DIALOGINTROTEXT("The code '"+USERCODE+"' is not valid.")
 DIALOGINTROTEXT("Please try again or click Cancel to exit.")
 DIALOGDISPLAY
 DIALOGCLOSE
END

PROCEDURE SHOWSUCCESSDIALOG
 POPUPMESSAGE(0, "The code '"+USERCODE+"' has been found and applied successfully.") 
END
